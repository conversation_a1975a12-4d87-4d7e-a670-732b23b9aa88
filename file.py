import streamlit as st
import os
import shutil
from langchain.vectorstores import DocArrayInMemorySearch
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.document_loaders import PyPDFLoader, CSVLoader, TextLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
import glob
from dotenv import load_dotenv, set_key
import pandas as pd
from datetime import datetime, timezone, timedelta
from crawler import crawl_and_save_as_text
from urllib.parse import urlparse
import subprocess
import time
import sqlite3
import requests  # 追加
import sys # Added for sys.executable
import hashlib
import time
import streamlit as st
from google import genai
from file_security import validate_file_upload, is_safe_filename, get_safe_filename
import json

# パスワードをハッシュ化して保存
load_dotenv()  # 環境変数を読み込む
default_password = "password"  # デフォルトのパスワード
stored_password_hash = hashlib.sha256(os.getenv('ADMIN_PASSWORD', default_password).encode()).hexdigest()

# 持久化された認証失敗記録の管理
AUTH_FAILURE_FILE = "auth_failures.json"

def load_auth_failures():
    """認証失敗記録を読み込む"""
    try:
        if os.path.exists(AUTH_FAILURE_FILE):
            with open(AUTH_FAILURE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('attempts', 0), data.get('last_failed_time', 0)
    except Exception as e:
        print(f"認証失敗記録の読み込みエラー: {e}")
    return 0, 0

def save_auth_failures(attempts, last_failed_time):
    """認証失敗記録を保存する"""
    try:
        data = {
            'attempts': attempts,
            'last_failed_time': last_failed_time,
            'updated_at': time.time()
        }
        with open(AUTH_FAILURE_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"認証失敗記録の保存エラー: {e}")

def reset_auth_failures():
    """認証失敗記録をリセットする"""
    try:
        if os.path.exists(AUTH_FAILURE_FILE):
            os.remove(AUTH_FAILURE_FILE)
    except Exception as e:
        print(f"認証失敗記録のリセットエラー: {e}")

def check_auth_lockout():
    """認証ロックアウト状態をチェックする"""
    attempts, last_failed_time = load_auth_failures()

    if attempts >= 3:
        time_since_last_failed = time.time() - last_failed_time
        lockout_duration = 300  # 5分間

        if time_since_last_failed >= lockout_duration:
            # ロックアウト期間が過ぎたのでリセット
            reset_auth_failures()
            return False, 0
        else:
            # まだロックアウト中
            remaining_time = lockout_duration - time_since_last_failed
            return True, remaining_time

    return False, 0

def record_auth_failure():
    """認証失敗を記録する"""
    attempts, _ = load_auth_failures()
    attempts += 1
    current_time = time.time()
    save_auth_failures(attempts, current_time)
    return attempts

# セッションの初期化
if "authenticated" not in st.session_state:
    st.session_state.authenticated = False
if "refresh_files" not in st.session_state:
    st.session_state.refresh_files = False
if "file_list_key" not in st.session_state:
    st.session_state.file_list_key = str(time.time())  # 初期キーを生成
if "selected_files" not in st.session_state:
    st.session_state.selected_files = set()  # 選択されたファイルを保持
if "temp_selected_files" not in st.session_state:
    st.session_state.temp_selected_files = set()  # 一時的な選択状態を保持
# アップロード状態の初期化（グローバルレベルで初期化）
if 'upload_state' not in st.session_state:
    st.session_state.upload_state = {
        'is_complete': False,
        'uploader_key': str(time.time())
    }
# ファイルアップローダーのキーを初期化
if 'file_uploader_key' not in st.session_state:
    st.session_state.file_uploader_key = str(time.time())

# パスワード入力
if not st.session_state.authenticated:
    # 持久化された認証失敗記録をチェック
    is_locked, remaining_time = check_auth_lockout()

    if is_locked:
        st.error("🔒 セキュリティロックアウト")
        st.warning(f"パスワードを3回間違えました。あと {int(remaining_time)} 秒待ってから再試行してください。")
        st.info("💡 ヒント: ページを刷新してもロックアウト時間は変わりません。")

        # 残り時間を表示するプログレスバー
        progress = 1 - (remaining_time / 300)  # 300秒が最大
        st.progress(progress, text=f"ロックアウト解除まで: {int(remaining_time)}秒")

        # 自動更新のため
        time.sleep(1)
        st.rerun()
    else:
        password = st.text_input("Enter password", type="password")

        # パスワードが入力された場合のみ処理
        if password:
            # 入力されたパスワードをハッシュ化
            password_hash = hashlib.sha256(password.encode()).hexdigest()

            # ハッシュ化されたパスワードと照合
            if password_hash == stored_password_hash:
                st.session_state.authenticated = True
                reset_auth_failures()  # 成功したのでリセット
                # CSSスタイルの追加
                st.markdown("""
                    <style>
                        .e10jh26i2 {
                            display: none;
                        }
                        .stButton>button {
                            width: 100%;
                            margin-top: 10px;
                        }
                        .file-list {
                            background-color: #f0f2f6;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 10px 0;
                        }
                        .upload-section {
                            background-color: #e8f4ea;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                        }
                        .delete-section {
                            background-color: #fde8e8;
                            padding: 20px;
                            border-radius: 10px;
                            margin: 20px 0;
                        }
                        .st-emotion-cache-yw8pof {
                            max-width: 1200px;
                        }
                        .st-emotion-cache-yw8pof {
                            padding-top: 60px;
                        }
                        [aria-label="Show password text"] {
                            display: none !important;
                        }
                        .st-emotion-cache-1w723zb {
                            max-width: 1200px !important;
                            width: 100%;
                            padding: 6rem 1rem 10rem;
                        }
                        /* データエディタのコンテナスタイル */
                        .stDataFrame {
                            width: 100% !important;
                            max-width: 1200px !important;
                        }
                        /* データエディタのテーブルスタイル */
                        .stDataFrame > div {
                            width: 100% !important;
                            max-width: 1200px !important;
                        }
                        /* チェックボックスを含むセルのスタイル */
                        .stDataFrame [data-testid="stDataFrameCell"] {
                            width: auto !important;
                        }
                        /* メインコンテナのスタイル */
                        .main > div {
                            max-width: 1200px !important;
                            width: 100% !important;
                        }
                        /* タブコンテンツのスタイル */
                        .stTabs [data-baseweb="tab-panel"] {
                            max-width: 1200px !important;
                            width: 100% !important;
                        }
                    </style>
                """, unsafe_allow_html=True)
                
                # 環境変数の読み込み    
                load_dotenv()
                
                # OpenAI API キーの確認
                if not os.environ.get("OPENAI_API_KEY"):
                    st.error("OpenAI API キーが設定されていません。.envファイルを確認してください。")
                
                # 操作するフォルダのパスを指定
                FILE_DIRECTORY = "./files"
                
                # ディレクトリがなければ作成
                if not os.path.exists(FILE_DIRECTORY):
                    os.makedirs(FILE_DIRECTORY)
                
                def list_files():
                    """ファイル一覧を取得し、データフレームとして返す"""
                    files = []
                    jst = timezone(timedelta(hours=9))  # 日本標準時(JST)
                    
                    for file in os.listdir(FILE_DIRECTORY):
                        file_path = os.path.join(FILE_DIRECTORY, file)
                        size = os.path.getsize(file_path) / 1024  # KBに変換
                        
                        # タイムスタンプを取得してJSTに変換
                        modified = datetime.fromtimestamp(os.path.getmtime(file_path))
                        modified_jst = modified.astimezone(jst)
                        
                        files.append({
                            'ダウンロード': False,  # 「ダウンロード」から「ダウンロード」に修正
                            'ファイル名': file,
                            'サイズ(KB)': f"{size:.1f}",
                            '最終更新': modified_jst.strftime('%Y-%m-%d %H:%M')
                        })
                    return pd.DataFrame(files)
                
                def download_files(files_to_download):
                    """選択されたファイルをダウンロードする関数"""
                    try:
                        if len(files_to_download) == 1:
                            # 単一ファイルの場合
                            file = files_to_download[0]
                            file_path = os.path.join(FILE_DIRECTORY, file)
                            with open(file_path, 'rb') as f:
                                file_content = f.read()
                            st.download_button(
                                label=f"📥 {file} をダウンロード",
                                data=file_content,
                                file_name=file,
                                mime='text/plain'
                            )
                        else:
                            # 複数ファイルの場合、ZIPファイルとして提供
                            import zipfile
                            import io
                            
                            # メモリ上でZIPファイルを作成
                            zip_buffer = io.BytesIO()
                            with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                                for file in files_to_download:
                                    file_path = os.path.join(FILE_DIRECTORY, file)
                                    zip_file.write(file_path, file)
                            
                            # ZIPファイルをダウンロード
                            st.download_button(
                                label=f"📥 選択した{len(files_to_download)}個のファイルをダウンロード",
                                data=zip_buffer.getvalue(),
                                file_name="selected_files.zip",
                                mime="application/zip"
                            )
                    except Exception as e:
                        st.error(f"ファイルのダウンロード準備中にエラーが発生しました: {e}")
                
                def upload_files(uploaded_files):
                    """複数ファイルをアップロード（セキュリティ検証付き）"""
                    if not uploaded_files:  # アップロードされたファイルがない場合は処理をスキップ
                        return

                    success_count = 0
                    error_messages = []
                    security_warnings = []

                    for uploaded_file in uploaded_files:
                        try:
                            # セキュリティ検証
                            is_valid, error_msg, file_info = validate_file_upload(uploaded_file)

                            if not is_valid:
                                error_messages.append(f"🚫 セキュリティ検証失敗 '{uploaded_file.name}': {error_msg}")
                                security_warnings.append(f"危険なファイル '{uploaded_file.name}' がブロックされました: {error_msg}")
                                continue

                            # 安全なファイル名を生成
                            safe_filename = get_safe_filename(uploaded_file.name)
                            if safe_filename != uploaded_file.name:
                                st.info(f"ファイル名を安全な形式に変更しました: '{uploaded_file.name}' → '{safe_filename}'")

                            # ファイル保存
                            file_path = os.path.join(FILE_DIRECTORY, safe_filename)

                            # 同名ファイルが存在する場合の処理
                            if os.path.exists(file_path):
                                base_name, ext = os.path.splitext(safe_filename)
                                counter = 1
                                while os.path.exists(file_path):
                                    safe_filename = f"{base_name}_{counter}{ext}"
                                    file_path = os.path.join(FILE_DIRECTORY, safe_filename)
                                    counter += 1
                                st.info(f"同名ファイルが存在するため、ファイル名を変更しました: {safe_filename}")

                            # ファイル保存
                            with open(file_path, "wb") as f:
                                f.write(uploaded_file.getbuffer())

                            success_count += 1
                            st.success(f"✅ '{safe_filename}' が正常にアップロードされました（サイズ: {file_info.get('size', 0):,} bytes）")

                        except Exception as e:
                            error_messages.append(f"ファイル '{uploaded_file.name}' のアップロードに失敗しました: {e}")

                    # セキュリティ警告の表示
                    if security_warnings:
                        st.error("🔒 セキュリティ警告:")
                        for warning in security_warnings:
                            st.error(f"• {warning}")

                    # エラーメッセージの表示
                    if error_messages:
                        st.error("❌ エラー:")
                        for error in error_messages:
                            st.error(f"• {error}")

                    if success_count > 0:
                        st.success(f"📁 {success_count}個のファイルが安全にアップロードされました")
                        # ファイル一覧の更新をトリガー
                        st.session_state.refresh_files = True
                        st.session_state.file_list_key = str(time.time())
                        # アップロード完了後にファイル選択をクリア
                        st.session_state.file_uploader = None
                
                def delete_files(files_to_delete):
                    """複数ファイルを削除"""
                    success_count = 0
                    for file in files_to_delete:
                        try:
                            file_path = os.path.join(FILE_DIRECTORY, file)
                            if os.path.exists(file_path):
                                os.remove(file_path)
                                success_count += 1
                        except Exception as e:
                            st.error(f"ファイル '{file}' の削除に失敗しました: {e}")
                    
                    if success_count > 0:
                        st.success(f"{success_count}個のファイルが削除されました")
                        # 削除後に選択状態をクリア
                        st.session_state.temp_selected_files.clear()
                        st.session_state.selected_files.clear()
                        # ファイル一覧を更新
                        st.session_state.file_list_key = str(time.time())
                
                def reload_files():
                    """アプリケーションを再起動する関数"""
                    try:
                        # インデックスディレクトリを削除
                        index_dir = "./vector_store"
                        if os.path.exists(index_dir):
                            shutil.rmtree(index_dir)
                        
                        # aichat.pyを再起動
                        venv_python = os.getcwd() + '/venv/bin/python'
                        subprocess.Popen([
                            venv_python, 
                            '-m', 
                            'streamlit', 
                            'run',
                            'aichat.py',
                            '--server.port=8501'
                        ])
                        
                        st.success("ファイルを再読み込みしました")
                        st.rerun()
                        
                    except Exception as e:
                        st.error(f"再読み込み中にエラーが発生しました: {e}")
                
                def crawl_and_save_as_text(url, filename, max_pages=100, progress_callback=None):
                    """URLからテキストを取得してファイルに保存する関数"""
                    try:
                        # crawler.pyの関数を呼び出す
                        from crawler import crawl_and_save_as_text as crawler_func
                        
                        # 無視する拡張子のリストを定義
                        ignored_extensions = [
                            # 画像ファイル
                            '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', 
                            '.ico', '.svg', '.tiff', '.heic',
                            # 文書ファイル
                            '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                            # 圧縮ファイル
                            '.zip', '.rar', '.7z', '.tar', '.gz',
                            # 実行ファイル
                            '.exe', '.dmg', '.app', '.apk',
                            # メディアファイル
                            '.mp3', '.mp4', '.wav', '.avi', '.mov', '.wmv',
                            # その他
                            '.csv', '.xml', '.json', '.sql', '.db'
                        ]
                        
                        return crawler_func(
                            url=url,
                            filename=filename,
                            max_pages=max_pages,
                            progress_callback=progress_callback,
                            ignored_extensions=ignored_extensions  # 無視する拡張子を渡す
                        )
                    except ImportError:
                        st.error("crawler.pyが見つかりません")
                        return False, None
                    except Exception as e:
                        st.error(f"クロール中にエラーが発生しました: {e}")
                        return False, None
                
                def get_domain_from_url(url):
                    """URLからドメイン名を抽出する関数"""
                    try:
                        parsed_url = urlparse(url)
                        # ドメイン名から'www.'を除去し、'.'を'-'に置換
                        domain = parsed_url.netloc.replace('www.', '').replace('.', '-')
                        return domain
                    except Exception:
                        return "unknown-domain"
                
                def delete_old_domain_files(domain):
                    """同じドメインの古いファイルを削除する関数"""
                    try:
                        files = os.listdir(FILE_DIRECTORY)
                        domain_files = [f for f in files if f.startswith(domain + "_")]
                        
                        # 日付でソート（新しい順）
                        domain_files.sort(reverse=True)
                        
                        # 最新以外を削除
                        if len(domain_files) > 1:
                            for old_file in domain_files[1:]:
                                file_path = os.path.join(FILE_DIRECTORY, old_file)
                                os.remove(file_path)
                            return len(domain_files) - 1
                        return 0
                    except Exception as e:
                        st.error(f"古いファイルの削除中にエラーが発生しました: {e}")
                        return 0

                def restart_aichat_process():
                    """Attempts to restart the aichat.py Streamlit application."""
                    try:
                        python_executable = sys.executable  # Use the interpreter running file.py

                        # Note: This starts a new process. It doesn't explicitly kill an old one.
                        # If aichat.py is already running on port 8501, Streamlit might handle
                        # the port conflict (e.g., by erroring or asking to use a new port,
                        # though Popen won't show interactive prompts).
                        subprocess.Popen([
                            python_executable,
                            '-m',
                            'streamlit',
                            'run',
                            'aichat.py',
                            '--server.port=8501' # Assuming aichat.py uses this port
                        ])
                        # Consider adding a st.toast or st.info message in file.py if needed,
                        # but it might disappear quickly due to the subsequent st.rerun().
                    except Exception as e:
                        st.error(f"An error occurred while trying to restart aichat.py: {e}")

                def save_env_var(key, value):
                    """環境変数を.envファイルに保存する"""
                    env_path = '.env'
                    set_key(env_path, key, value)
                    os.environ[key] = value
                
                def load_api_settings():
                    """APIの設定を読み込む"""
                    load_dotenv(override=True)  # 既存の環境変数を上書き
                    
                    # プロバイダー設定
                    active_provider = os.getenv('ACTIVE_PROVIDER', 'openai')
                    embedding_provider = os.getenv('EMBEDDING_PROVIDER', 'openai')
                    
                    # 各プロバイダーの設定を読み込む
                    settings = {
                        # プロバイダー設定
                        'ACTIVE_PROVIDER': active_provider,
                        'EMBEDDING_PROVIDER': embedding_provider,
                        
                        # OpenAI設定
                        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY', ''),
                        'EMBEDDING_MODEL': os.getenv('EMBEDDING_MODEL', 'text-embedding-3-small'),
                        'CHAT_MODEL': os.getenv('CHAT_MODEL', 'gpt-4o-mini'),
                        
                        # Deepseek設定
                        'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', ''),
                        'DEEPSEEK_MODEL': os.getenv('DEEPSEEK_MODEL', 'deepseek-chat'),
                        
                        # Ollama設定
                        'OLLAMA_BASE_URL': os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434'),
                        'OLLAMA_EMBED_MODEL': os.getenv('OLLAMA_EMBED_MODEL', 'llama2'),
                        'OLLAMA_CHAT_MODEL': os.getenv('OLLAMA_CHAT_MODEL', 'llama2'),

                        # Gemini設定
                        'GEMINI_API_KEY': os.getenv('GEMINI_API_KEY', ''),
                        'GEMINI_MODEL': os.getenv('GEMINI_MODEL', 'gemini-2.5-pro-preview-06-05'),
                    }
                    
                    return settings
                
                def get_ollama_models(base_url):
                    """Ollamaサーバーから利用可能なモデル一覧を取得"""
                    try:
                        response = requests.get(f"{base_url}/api/tags")
                        if response.status_code == 200:
                            models = response.json()
                            return [model['name'] for model in models['models']]
                        return ["llama2"]  # デフォルトモデル
                    except Exception as e:
                        st.warning(f"Ollamaサーバーからモデル一覧を取得できませんでした: {e}")
                        return ["llama2"]  # エラー時はデフォルトモデルを返す
                
                def get_openai_embedding_models(api_key):
                    """OpenAI APIから利用可能なEmbeddingモデル一覧を取得する関数"""
                    try:
                        headers = {
                            "Authorization": f"Bearer {api_key}",
                            "Content-Type": "application/json"
                        }
                        response = requests.get("https://api.openai.com/v1/models", headers=headers)
                        if response.status_code == 200:
                            models = response.json()
                            # Embeddingモデルのみをフィルタリング
                            embedding_models = [
                                model['id'] for model in models['data']
                                if model['id'].startswith('text-embedding-')
                            ]
                            # モデルを名前でソート
                            embedding_models.sort()
                            return embedding_models
                        return ["text-embedding-3-small"]  # デフォルトモデル
                    except Exception as e:
                        st.warning(f"OpenAI Embeddingモデル一覧の取得に失敗しました: {e}")
                        return ["text-embedding-3-small"]  # エラー時はデフォルトモデルを返す
                
                def get_openai_models(api_key):
                    """OpenAI APIから利用可能なモデル一覧を取得する関数"""
                    try:
                        headers = {
                            "Authorization": f"Bearer {api_key}",
                            "Content-Type": "application/json"
                        }
                        response = requests.get("https://api.openai.com/v1/models", headers=headers)
                        if response.status_code == 200:
                            models = response.json()
                            # チャットモデルのみをフィルタリング
                            chat_models = [
                                model['id'] for model in models['data']
                                if model['id'].startswith(('gpt-3.5', 'gpt-4'))
                            ]
                            # モデルを名前でソート
                            chat_models.sort()
                            return chat_models
                        return ["gpt-4o-mini"]  # デフォルトモデル
                    except Exception as e:
                        st.warning(f"OpenAIモデル一覧の取得に失敗しました: {e}")
                        return ["gpt-4o-mini"]  # エラー時はデフォルトモデルを返す
                
                def get_gemini_models(api_key):
                    """Gemini APIから利用可能なチャットモデル一覧を取得する関数"""
                    try:
                        from google import genai
                        if api_key:
                            client = genai.Client(api_key=api_key)
                        else:
                            client = genai.Client()  # 環境変数 GAI_API_KEY などに設定済みの場合

                        chat_models = []
                        for m in client.models.list():
                            if "generateContent" in m.supported_actions:
                                model_name = m.name.replace("models/", "")
                                chat_models.append(model_name)

                        chat_models.sort()
                        return chat_models if chat_models else ["gemini-2.0-flash"]  # モデルが空ならデフォルト

                    except Exception as e:
                        st.warning(f"Geminiモデル一覧の取得に失敗しました: {e}")
                        return ["gemini-2.0-flash"]  # エラー時はデフォルトモデルを返す

                def display_api_settings():
                    """API設定画面を表示する"""
                    settings = load_api_settings()
                    
                    st.subheader("🔑 API設定")
                    
                    # API設定状態を管理
                    if 'api_change_triggered' not in st.session_state:
                        st.session_state.api_change_triggered = False
                    if 'api_form_key' not in st.session_state:
                        st.session_state.api_form_key = str(time.time())
                    if 'api_change_success' not in st.session_state:
                        st.session_state.api_change_success = False
                    
                    # --- NEW: Initialize session state from .env on first load ---
                    if "ACTIVE_PROVIDER" not in st.session_state:
                        st.session_state["ACTIVE_PROVIDER"] = os.getenv("ACTIVE_PROVIDER", settings['ACTIVE_PROVIDER'])
                    if "chat_model" not in st.session_state:
                        st.session_state["chat_model"] = os.getenv("CHAT_MODEL", settings['CHAT_MODEL'])
                    if "gemini_model" not in st.session_state:
                        st.session_state.gemini_model = os.getenv("GEMINI_MODEL", settings['GEMINI_MODEL'])
                    # ----------------------------------------------------------

                    # フォームの状態を保持
                    if 'openai_api_key' not in st.session_state:
                        st.session_state.openai_api_key = settings['OPENAI_API_KEY']
                    if 'embedding_model' not in st.session_state:
                        st.session_state.embedding_model = settings['EMBEDDING_MODEL']
                    if 'chat_model' not in st.session_state:
                        st.session_state.chat_model = settings['CHAT_MODEL']
                    if 'gemini_api_key' not in st.session_state:
                        st.session_state.gemini_api_key = settings['GEMINI_API_KEY']
                    if 'gemini_model' not in st.session_state:
                        st.session_state.gemini_model = settings['GEMINI_MODEL']
                    
                    # API設定フォームをコンテナで囲む
                    with st.form(key=f"api_form_{st.session_state.api_form_key}", clear_on_submit=True):
                        # Embedding設定
                        st.markdown("### Embedding設定")
                        embed_provider = st.radio(
                            "使用するEmbeddingモデル",
                            options=["OpenAI"],
                            index=0 if settings['EMBEDDING_PROVIDER'] == 'openai' else 1,
                            horizontal=True
                        )
                        
                        if embed_provider == "OpenAI":
                            openai_api_key = st.text_input(
                                "OpenAI API Key",
                                value=st.session_state.openai_api_key,
                                type="password",
                                help="Embeddingに必要なOpenAI APIキー"
                            )
                            
                            # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                            if openai_api_key:
                                try:
                                    embedding_models = get_openai_embedding_models(openai_api_key)
                                    # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                                    current_model = st.session_state.embedding_model if st.session_state.embedding_model in embedding_models else "text-embedding-3-small"
                                    embedding_model = st.selectbox(
                                        "Embeddingモデル",
                                        options=embedding_models,
                                        index=embedding_models.index(current_model)
                                    )
                                except Exception as e:
                                    st.error(f"Embeddingモデル一覧の取得に失敗しました: {e}")
                                    embedding_model = st.session_state.embedding_model
                            else:
                                # APIキーが入力されていない場合は、現在の設定値を使用
                                embedding_model = st.session_state.embedding_model
                                st.warning("OpenAI APIキーを入力すると、利用可能なEmbeddingモデル一覧が表示されます。")
                        
                        st.markdown("---")
                        
                        # チャットモデルプロバイダーの選択
                        st.markdown("### チャットモデル設定")
                        if "ACTIVE_PROVIDER" not in st.session_state:
                            st.session_state["ACTIVE_PROVIDER"] = "openai"

                        # 触发切换时刷新
                        chat_provider = st.radio(
                            "使用するチャットモデル",
                            options=["OpenAI", "Gemini"],
                            index={"openai": 0, "gemini": 1}[st.session_state["ACTIVE_PROVIDER"].lower()],
                            horizontal=True,
                            key="chat_provider"  # 把选择绑定到 session_state["chat_provider"]
                        )

                        # 如果切换了 provider，触发 rerun
                        if chat_provider != st.session_state.get("ACTIVE_PROVIDER"):
                            st.session_state["ACTIVE_PROVIDER"] = chat_provider
                            st.rerun()

                        if st.session_state["ACTIVE_PROVIDER"] == "OpenAI":
                            if embed_provider != "OpenAI":
                                openai_api_key = st.text_input(
                                    "OpenAI API Key",
                                    value=st.session_state.openai_api_key,
                                    type="password"
                                )
                            
                            # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                            if openai_api_key:
                                try:
                                    chat_models = get_openai_models(openai_api_key)
                                    # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                                    current_model = st.session_state.chat_model if st.session_state.chat_model in chat_models else "gpt-4o-mini"
                                    chat_model = st.selectbox(
                                        "OpenAIチャットモデル",
                                        options=chat_models,
                                        index=chat_models.index(current_model)
                                    )
                                except Exception as e:
                                    st.error(f"モデル一覧の取得に失敗しました: {e}")
                                    chat_model = st.session_state.chat_model
                            else:
                                # APIキーが入力されていない場合は、現在の設定値を使用
                                chat_model = st.session_state.chat_model
                                st.warning("OpenAI APIキーを入力すると、利用可能なモデル一覧が表示されます。")

                        if st.session_state["ACTIVE_PROVIDER"] == "Gemini":
                            if embed_provider != "Gemini":
                                gemini_api_key = st.text_input(
                                    "Gemini API Key",
                                    value=st.session_state.gemini_api_key,
                                    type="password"
                                )
                            
                            # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                            if gemini_api_key:
                                try:
                                    chat_models = get_gemini_models(gemini_api_key)
                                    # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                                    current_model = st.session_state.gemini_model if st.session_state.gemini_model in chat_models else "gemini-2.5-pro-preview-06-05"
                                    chat_model = st.selectbox(
                                        "Geminiチャットモデル",
                                        options=chat_models,
                                        index=chat_models.index(current_model)
                                    )
                                except Exception as e:
                                    st.error(f"モデル一覧の取得に失敗しました: {e}")
                                    chat_model = st.session_state.chat_model
                            else:
                                # APIキーが入力されていない場合は、現在の設定値を使用
                                chat_model = st.session_state.chat_model
                                st.warning("Gemini APIキーを入力すると、利用可能なモデル一覧が表示されます。")
                            
                        # 設定の保存ボタン
                        submitted = st.form_submit_button(
                            "設定を保存",
                            type="primary",
                            disabled=st.session_state.api_change_triggered
                        )
                            
                        if submitted:
                            try:
                                if openai_api_key:
                                    save_env_var('OPENAI_API_KEY', openai_api_key)
                                    st.session_state.openai_api_key = openai_api_key
                                if embed_provider == "OpenAI":
                                    save_env_var('EMBEDDING_MODEL', embedding_model)
                                    save_env_var('EMBEDDING_PROVIDER', 'openai')
                                    st.session_state.embedding_model = embedding_model
                                if chat_provider == "OpenAI":
                                    save_env_var('CHAT_MODEL', chat_model)
                                    st.session_state["chat_model"] = chat_model
                                    st.session_state["ACTIVE_PROVIDER"] = "openai"
                                    save_env_var('ACTIVE_PROVIDER', "openai")
                                if chat_provider == "Gemini":
                                    save_env_var('GEMINI_MODEL', chat_model)
                                    st.session_state["chat_model"] = chat_model
                                    st.session_state["ACTIVE_PROVIDER"] = "gemini"
                                    save_env_var('ACTIVE_PROVIDER', "gemini")
                                
                                # --- NEW: Reload from .env and update session state ---
                                load_dotenv(override=True)
                                st.session_state["ACTIVE_PROVIDER"] = os.getenv("ACTIVE_PROVIDER", st.session_state.get("ACTIVE_PROVIDER", "openai"))
                                st.session_state.gemini_model = os.getenv("GEMINI_MODEL", st.session_state.get("gemini_model", "gemini-2.5-pro-preview-06-05"))
                                # ------------------------------------------------------

                                # API変更処理中であることを記録
                                st.session_state.api_change_triggered = True
                                    
                                # 成功状態を設定
                                st.session_state.api_change_success = True
                                    
                                # フォームをリセット
                                st.session_state.api_change_triggered = False
                                st.session_state.api_form_key = str(time.time())
                                    
                                reload_files()

                                # 画面を更新
                                st.rerun()
                            except Exception as e:
                                st.error(f"API設定の保存中にエラーが発生しました: {e}")
                                st.session_state.api_change_triggered = False
                
                def load_parameter_settings():
                    """パラメータ設定を読み込む"""
                    load_dotenv(override=True)
                    
                    settings = {
                        'CHUNK_SIZE': int(os.getenv('CHUNK_SIZE', '500')),
                        'CHUNK_OVERLAP': int(os.getenv('CHUNK_OVERLAP', '50')),
                        'SYSTEM_PROMPT': os.getenv('SYSTEM_PROMPT', """あなたは丁寧で正確な回答を提供するアシスタントです。
以下の指示に従って回答してください：
1. 提供された文脈を参考に、日本語で質問に対して具体的に回答してください
2. 提供された文脈に無い情報は提供せず、「申し訳ございません、その質問にはお答えすることができません。」のみ回答してください
3. 参考にした文脈の引用は回答しないでください
4. 必要に応じて、参考にした文書の内容を引用してください
5. 質問の意図を理解し、関連する情報があれば柔軟に解釈して回答してください
6. 文脈内の情報が部分的にでも質問に関連する場合は、その情報を活用して回答してください""")
                    }
                    return settings
                
                def display_parameter_settings():
                    """パラメータ設定画面を表示する"""
                    settings = load_parameter_settings()
                    
                    st.subheader("⚙️ パラメータ設定")
                    
                    # パラメータ設定状態を管理
                    if 'parameter_change_triggered' not in st.session_state:
                        st.session_state.parameter_change_triggered = False
                    if 'parameter_form_key' not in st.session_state:
                        st.session_state.parameter_form_key = str(time.time())
                    if 'parameter_change_success' not in st.session_state:
                        st.session_state.parameter_change_success = False
                    
                    # フォームの状態を保持
                    if 'chunk_size' not in st.session_state:
                        st.session_state.chunk_size = settings['CHUNK_SIZE']
                    if 'chunk_overlap' not in st.session_state:
                        st.session_state.chunk_overlap = settings['CHUNK_OVERLAP']
                    if 'system_prompt' not in st.session_state:
                        st.session_state.system_prompt = settings['SYSTEM_PROMPT']
                    
                    # パラメータ設定フォームをコンテナで囲む
                    with st.form(key=f"parameter_form_{st.session_state.parameter_form_key}", clear_on_submit=True):
                        # チャンクサイズ設定
                        st.markdown("### テキスト分割設定")
                        col1, col2 = st.columns(2)
                        
                        with col1:
                            chunk_size = st.number_input(
                                "チャンクサイズ",
                                min_value=100,
                                max_value=2000,
                                value=st.session_state.chunk_size,
                                step=50,
                                help="テキストを分割する際の1チャンクあたりの文字数。大きすぎるとトークン制限エラーが発生する可能性があります。"
                            )
                        
                        with col2:
                            chunk_overlap = st.number_input(
                                "オーバーラップ",
                                min_value=0,
                                max_value=500,
                                value=st.session_state.chunk_overlap,
                                step=10,
                                help="チャンク間で重複させる文字数。文脈の連続性を保つために使用されます。"
                            )
                        
                        # システムプロンプト設定
                        st.markdown("### システムプロンプト設定")
                        system_prompt = st.text_area(
                            "システムプロンプト",
                            value=st.session_state.system_prompt,
                            height=300,
                            help="AIアシスタントの振る舞いを定義するプロンプト。回答の形式や制約を指定できます。"
                        )
                        
                        # 設定の保存ボタン
                        submitted = st.form_submit_button(
                            "設定を保存",
                            type="primary",
                            disabled=st.session_state.parameter_change_triggered
                        )
                        
                        if submitted:
                            try:
                                # 環境変数の保存
                                save_env_var('CHUNK_SIZE', str(chunk_size))
                                save_env_var('CHUNK_OVERLAP', str(chunk_overlap))
                                save_env_var('SYSTEM_PROMPT', system_prompt)
                                
                                # 状態を更新
                                st.session_state.chunk_size = chunk_size
                                st.session_state.chunk_overlap = chunk_overlap
                                st.session_state.system_prompt = system_prompt
                                
                                # パラメータ変更処理中であることを記録
                                st.session_state.parameter_change_triggered = True
                                
                                # 成功状態を設定
                                st.session_state.parameter_change_success = True
                                
                                # フォームをリセット
                                st.session_state.parameter_change_triggered = False
                                st.session_state.parameter_form_key = str(time.time())
                                
                                # 画面を更新
                                st.rerun()
                                
                                # ベクトルストアの再構築が必要な場合は警告を表示
                                if (chunk_size != settings['CHUNK_SIZE'] or 
                                    chunk_overlap != settings['CHUNK_OVERLAP']):
                                    st.warning("""
                                        チャンクサイズまたはオーバーラップを変更しました。
                                        新しい設定を反映するには、ベクトルストアを再構築する必要があります。
                                        以下の手順で再構築してください：
                                        1. ファイル管理タブで「ファイルを再読み込み」を実行
                                        2. または、vector_storeディレクトリを手動で削除
                                    """)
                                
                            except Exception as e:
                                st.error(f"設定の保存中にエラーが発生しました: {e}")
                                st.session_state.parameter_change_triggered = False
                
                def display_password_settings():
                    """パスワード設定画面を表示する関数"""
                    global stored_password_hash
                    
                    st.subheader("🔒 パスワード設定")
                    
                    # パスワード入力状態を管理
                    if 'password_change_triggered' not in st.session_state:
                        st.session_state.password_change_triggered = False
                    if 'password_form_key' not in st.session_state:
                        st.session_state.password_form_key = str(time.time())
                    if 'password_change_success' not in st.session_state:
                        st.session_state.password_change_success = False
                    
                    # フォームの状態を保持
                    if 'current_password' not in st.session_state:
                        st.session_state.current_password = ""
                    if 'new_password' not in st.session_state:
                        st.session_state.new_password = ""
                    if 'confirm_password' not in st.session_state:
                        st.session_state.confirm_password = ""
                    
                    # パスワード入力フォームをコンテナで囲む
                    with st.form(key=f"password_form_{st.session_state.password_form_key}", clear_on_submit=True):
                        # 現在のパスワード確認
                        current_password = st.text_input(
                            "現在のパスワード",
                            type="password",
                            key="current_password_input",
                            disabled=st.session_state.password_change_triggered,
                            value=st.session_state.current_password
                        )
                        
                        # 新しいパスワード入力
                        new_password = st.text_input(
                            "新しいパスワード",
                            type="password",
                            key="new_password_input",
                            disabled=st.session_state.password_change_triggered,
                            value=st.session_state.new_password
                        )
                        
                        # 新しいパスワード確認
                        confirm_password = st.text_input(
                            "新しいパスワード（確認）",
                            type="password",
                            key="confirm_password_input",
                            disabled=st.session_state.password_change_triggered,
                            value=st.session_state.confirm_password
                        )
                        
                        # パスワード変更ボタン
                        submitted = st.form_submit_button(
                            "パスワードを変更",
                            type="primary",
                            disabled=st.session_state.password_change_triggered
                        )
                        
                        if submitted:
                            try:
                                if not current_password or not new_password or not confirm_password:
                                    st.error("すべての項目を入力してください。")
                                    return
                                
                                # 現在のパスワードの確認
                                current_password_hash = hashlib.sha256(current_password.encode()).hexdigest()
                                if current_password_hash != stored_password_hash:
                                    st.error("現在のパスワードが正しくありません。")
                                    return
                                
                                # 新しいパスワードの確認
                                if new_password != confirm_password:
                                    st.error("新しいパスワードが一致しません。")
                                    return
                                
                                # パスワードの長さチェック
                                if len(new_password) < 8:
                                    st.error("パスワードは8文字以上である必要があります。")
                                    return
                                
                                # パスワード変更処理中であることを記録
                                st.session_state.password_change_triggered = True
                                
                                # 環境変数の更新
                                save_env_var('ADMIN_PASSWORD', new_password)
                                
                                # グローバル変数の更新
                                stored_password_hash = hashlib.sha256(new_password.encode()).hexdigest()
                                
                                # 入力値をクリア
                                st.session_state.current_password = ""
                                st.session_state.new_password = ""
                                st.session_state.confirm_password = ""
                                
                                # 成功状態を設定
                                st.session_state.password_change_success = True

                                # Attempt to restart aichat.py so it picks up the new password
                                restart_aichat_process()

                                # フォームをリセット
                                st.session_state.password_change_triggered = False
                                st.session_state.password_form_key = str(time.time())

                                # 画面を更新 (for file.py)
                                st.rerun()
                            except Exception as e:
                                st.error(f"パスワードの変更中にエラーが発生しました: {e}")
                                st.session_state.password_change_triggered = False
                
                def main():
                    st.title("📁 ファイル管理システム")
                    st.session_state.setdefault("password_change_triggered", False)
                    
                    # パスワード変更成功メッセージ用のコンテナ
                    if 'password_change_success' in st.session_state and st.session_state.password_change_success:
                        success_container = st.empty()
                        success_container.success("パスワードを変更しました。")
                        # 3秒後にメッセージを消す
                        time.sleep(3)
                        success_container.empty()
                        # 成功状態をリセット
                        st.session_state.password_change_success = False
                        st.rerun()
                    
                    # パラメータ設定成功メッセージ用のコンテナ
                    if 'parameter_change_success' in st.session_state and st.session_state.parameter_change_success:
                        success_container = st.empty()
                        success_container.success("パラメータ設定を保存しました。")
                        # 3秒後にメッセージを消す
                        time.sleep(3)
                        success_container.empty()
                        # 成功状態をリセット
                        st.session_state.parameter_change_success = False
                        st.rerun()
                    
                    # API設定成功メッセージ用のコンテナ
                    if 'api_change_success' in st.session_state and st.session_state.api_change_success:
                        success_container = st.empty()
                        success_container.success("API設定を保存しました。")
                        # 3秒後にメッセージを消す
                        time.sleep(3)
                        success_container.empty()
                        # 成功状態をリセット
                        st.session_state.api_change_success = False
                        st.rerun()
                    
                    # タブを作成
                    tab1, tab2, tab3, tab4, tab5 = st.tabs(["ファイル管理", "チャット履歴", "API設定", "パラメータ設定", "パスワード設定"])
                    
                    with tab1:
                        # ファイル一覧用のコンテナを作成
                        file_list_container = st.empty()
                        
                        def update_file_list():
                            """ファイル一覧を更新する関数"""
                            with file_list_container.container():
                                st.subheader("📋 ファイル一覧")
                                files_df = list_files()
                                if not files_df.empty:
                                    current_files = set(files_df['ファイル名'].tolist())
                                    
                                    # DataFrameの「選択」列をsession_stateに合わせて更新
                                    files_df['選択'] = files_df['ファイル名'].isin(st.session_state.temp_selected_files)

                                    # DataEditorで編集
                                    editor_key = f"file_list_editor_{st.session_state.file_list_key}"
                                    edited_df = st.data_editor(
                                        files_df,
                                        disabled=["ファイル名", "サイズ(KB)", "最終更新"],
                                        hide_index=True,
                                        column_config={
                                            "選択": st.column_config.CheckboxColumn(
                                                "選択",
                                                help="ダウンロードまたは削除するファイルを選択",
                                                default=False,
                                                required=True,
                                            ),
                                            "ファイル名": st.column_config.TextColumn(
                                                "ファイル名",
                                                help="ファイルの名前",
                                                width="large",
                                            ),
                                            "サイズ(KB)": st.column_config.TextColumn(
                                                "サイズ(KB)",
                                                help="ファイルサイズ（キロバイト）",
                                                width="medium",
                                            ),
                                            "最終更新": st.column_config.TextColumn(
                                                "最終更新",
                                                help="ファイルの最終更新日時",
                                                width="medium",
                                            ),
                                        },
                                        column_order=["選択", "ファイル名", "サイズ(KB)", "最終更新"],
                                        use_container_width=True,
                                        key=editor_key
                                    )

                                    # 編集後の選択状態を更新
                                    if edited_df is not None:
                                        selected_files = set(edited_df[edited_df['選択']]['ファイル名'].tolist())
                                        if selected_files != st.session_state.temp_selected_files:
                                            st.session_state.temp_selected_files = selected_files
                                            st.rerun()

                                    if st.session_state.temp_selected_files:
                                        st.markdown("<br>", unsafe_allow_html=True)
                                        col1, col2, col3 = st.columns([1, 2, 1])
                                        with col2:
                                            download_key = f"download_files_button_{st.session_state.file_list_key}"
                                            if st.button(
                                                f"📥 選択した{len(st.session_state.temp_selected_files)}個のファイルをダウンロード",
                                                type="primary",
                                                key=download_key
                                            ):
                                                selected_files = list(st.session_state.temp_selected_files)
                                                download_files(selected_files)
                                            delete_key = f"delete_files_button_{st.session_state.file_list_key}"
                                            if st.button(
                                                f"🗑️ 選択した{len(st.session_state.temp_selected_files)}個のファイルを削除",
                                                type="primary",
                                                key=delete_key
                                            ):
                                                selected_files = list(st.session_state.temp_selected_files)
                                                delete_files(selected_files)
                                                st.session_state.temp_selected_files.clear()
                                                st.session_state.selected_files.clear()
                                                st.session_state.file_list_key = str(time.time())
                                                st.rerun()
                                else:
                                    st.info("ファイルが存在しません")
                                    st.session_state.temp_selected_files.clear()
                                    st.session_state.selected_files.clear()
                        
                        # 初期表示
                        update_file_list()
                        
                        # アップロードセクション
                        st.markdown("---")
                        with st.expander("📤 ファイルのアップロード", expanded=True):
                            
                            # ファイルアップローダーの表示
                            uploaded_files = st.file_uploader(
                                "ファイルを選択してください（複数可）(ファイル名は英数字に変換して下さい)",
                                accept_multiple_files=True,
                                type=["txt", "pdf", "csv", "xlsx", "xls"],
                                key=f"uploader_{st.session_state.upload_state['uploader_key']}"
                            )
                            
                            # ファイルが選択された場合の処理
                            if uploaded_files and not st.session_state.upload_state['is_complete']:
                                success_count = 0
                                error_messages = []
                                security_warnings = []

                                # ファイルのアップロード処理（セキュリティ検証付き）
                                for uploaded_file in uploaded_files:
                                    try:
                                        # セキュリティ検証
                                        is_valid, error_msg, file_info = validate_file_upload(uploaded_file)

                                        if not is_valid:
                                            error_messages.append(f"🚫 セキュリティ検証失敗 '{uploaded_file.name}': {error_msg}")
                                            security_warnings.append(f"危険なファイル '{uploaded_file.name}' がブロックされました: {error_msg}")
                                            continue

                                        # 安全なファイル名を生成
                                        safe_filename = get_safe_filename(uploaded_file.name)
                                        if safe_filename != uploaded_file.name:
                                            st.info(f"ファイル名を安全な形式に変更しました: '{uploaded_file.name}' → '{safe_filename}'")

                                        # ファイル保存
                                        file_path = os.path.join(FILE_DIRECTORY, safe_filename)

                                        # 同名ファイルが存在する場合の処理
                                        if os.path.exists(file_path):
                                            base_name, ext = os.path.splitext(safe_filename)
                                            counter = 1
                                            while os.path.exists(file_path):
                                                safe_filename = f"{base_name}_{counter}{ext}"
                                                file_path = os.path.join(FILE_DIRECTORY, safe_filename)
                                                counter += 1
                                            st.info(f"同名ファイルが存在するため、ファイル名を変更しました: {safe_filename}")

                                        # ファイル保存
                                        with open(file_path, "wb") as f:
                                            f.write(uploaded_file.getbuffer())

                                        success_count += 1
                                        st.success(f"✅ '{safe_filename}' が正常にアップロードされました（サイズ: {file_info.get('size', 0):,} bytes）")

                                    except Exception as e:
                                        error_messages.append(f"ファイル '{uploaded_file.name}' のアップロードに失敗しました: {e}")

                                # セキュリティ警告の表示
                                if security_warnings:
                                    st.error("🔒 セキュリティ警告:")
                                    for warning in security_warnings:
                                        st.error(f"• {warning}")

                                # エラーメッセージの表示
                                if error_messages:
                                    st.error("❌ エラー:")
                                    for error in error_messages:
                                        st.error(f"• {error}")

                                # 結果の表示
                                if success_count > 0:
                                    st.success(f"📁 {success_count}個のファイルが安全にアップロードされました")
                                    # ファイル一覧の更新をトリガー
                                    st.session_state.file_list_key = str(time.time())
                                    # アップロード状態を更新
                                    st.session_state.upload_state['is_complete'] = True
                                    st.session_state.upload_state['uploader_key'] = str(time.time())
                                    # 画面を更新
                                    st.rerun()
                                elif not error_messages and not security_warnings:
                                    st.warning("アップロードするファイルが選択されていません")
                                
                                # エラーメッセージの表示
                                for error in error_messages:
                                    st.error(error)
                            
                            # ファイルが選択されていない場合はアップロード状態をリセット
                            elif not uploaded_files:
                                st.session_state.upload_state['is_complete'] = False
                        
                        # クローリングセクション
                        st.markdown("---")
                        with st.expander("🌐 ウェブサイトから学習データを追加", expanded=True):
                            # 複数URLの入力を許可
                            urls_input = st.text_area(
                                "クロールするURLを入力してください（1行に1つのURL）",
                                height=100,
                                help="複数のURLを入力する場合は、1行に1つのURLを入力してください"
                            )
                            
                            if urls_input:
                                # 入力されたURLを行で分割してリスト化
                                urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
                                
                                # 各URLのドメインとファイル名を生成
                                url_files = []
                                for url in urls:
                                    if not url.startswith(('http://', 'https://')):
                                        url = 'https://' + url
                                    domain = get_domain_from_url(url)
                                    current_date = datetime.now().strftime('%Y%m%d')
                                    filename = f"{domain}_{current_date}.txt"
                                    url_files.append((url, filename))
                                
                                # クロール開始ボタン
                                if st.button("クロールしてテキスト化", disabled=not urls):
                                    # 進捗表示用のコンテナを作成
                                    progress_container = st.empty()
                                    status_container = st.empty()
                                    result_container = st.empty()
                                    
                                    def update_progress(progress, status):
                                        if progress is not None:
                                            progress_container.progress(progress)
                                        if status:
                                            status_container.text(status)
                                    
                                    # 全体の進捗を計算するための変数
                                    total_urls = len(urls)
                                    success_count = 0
                                    
                                    for i, (url, filename) in enumerate(url_files):
                                        # 個別のURLの進捗を全体の進捗に変換するラッパー関数
                                        def progress_wrapper(progress, status):
                                            if progress is not None:
                                                overall_progress = (i + progress) / total_urls
                                                update_progress(overall_progress, f"URL {i+1}/{total_urls}: {status}")
                                            else:
                                                update_progress(None, status)
                                        
                                        success, output_filename = crawl_and_save_as_text(
                                            url, 
                                            filename, 
                                            max_pages=100, 
                                            progress_callback=progress_wrapper
                                        )
                                        
                                        if success:
                                            success_count += 1
                                            # 同じドメインの古いファイルを削除
                                            domain = get_domain_from_url(url)
                                            deleted_count = delete_old_domain_files(domain)
                                    
                                    # 最終的な結果を表示（3秒後に消える）
                                    success_message = f"{success_count}/{total_urls}のURLのクロールが完了しました。"
                                    result_container.success(success_message)
                                    
                                    # 進捗表示をクリア
                                    progress_container.empty()
                                    status_container.empty()
                                    
                                    # ファイル一覧のキーを更新してから表示を更新
                                    st.session_state.file_list_key = str(time.time())
                                    update_file_list()  # ファイル一覧を更新
                                    
                                    # 3秒後に結果メッセージを消す
                                    time.sleep(3)
                                    result_container.empty()
                
                    with tab2:
                        display_chat_logs()  # チャット履歴表示機能
                
                    with tab3:
                        display_api_settings()  # API設定表示機能
                
                    with tab4:
                        display_parameter_settings()  # パラメータ設定表示機能
                
                    with tab5:
                        display_password_settings()  # パスワード設定表示機能
                
                def display_chat_logs():
                    """チャット履歴を表示する関数"""
                    DATABASE = 'aichat_log.db'
                
                    # DBコネクション取得関数
                    def get_db_connection():
                        conn = sqlite3.connect(DATABASE)
                        conn.row_factory = sqlite3.Row
                        return conn
                
                    # テーブル作成関数
                    def create_logs_table():
                        conn = get_db_connection()
                        try:
                            conn.execute('''
                                CREATE TABLE IF NOT EXISTS logs (
                                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                                    user_question TEXT NOT NULL,
                                    ai_answer TEXT NOT NULL,
                                    reference_document TEXT,
                                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                                )
                            ''')
                            conn.commit()
                        except Exception as e:
                            st.error(f"テーブル作成中にエラーが発生しました: {e}")
                        finally:
                            conn.close()
                
                    # ログ削除関数
                    def delete_log(log_id):
                        conn = get_db_connection()
                        conn.execute('DELETE FROM logs WHERE id = ?', (log_id,))
                        conn.commit()
                        conn.close()
                        st.success(f"Log with ID {log_id} deleted successfully!")
                        st.rerun()
                
                    # テーブルが存在しない場合は作成
                    create_logs_table()
                
                    # DBからログを取得
                    conn = get_db_connection()
                    logs = conn.execute("SELECT id, user_question, ai_answer, reference_document, timestamp FROM logs").fetchall()
                    conn.close()
                
                    # DataFrameに変換
                    logs_df = pd.DataFrame(logs, columns=['ID', 'User Question', 'AI Answer', 'Reference Document', 'Timestamp'])
                    logs_df['選択'] = False
                
                    if logs_df.empty:
                        st.warning("チャット履歴が存在しません")
                        return
                
                    # フィルターセクション
                    col1, col2, col3 = st.columns([2, 1, 1])
                    with col1:
                        search_query = st.text_input("テキスト検索", "")
                    with col2:
                        start_date = st.date_input("開始日", value=None, key="start_date")
                    with col3:
                        end_date = st.date_input("終了日", value=None, key="end_date")
                
                    # フィルター適用
                    if search_query:
                        mask = (
                            logs_df['User Question'].str.contains(search_query, case=False, na=False) |
                            logs_df['AI Answer'].str.contains(search_query, case=False, na=False) |
                            logs_df['Reference Document'].str.contains(search_query, case=False, na=False)
                        )
                        logs_df = logs_df[mask]
                
                    # 日付フィルター
                    if start_date or end_date:
                        logs_df['Date'] = pd.to_datetime(logs_df['Timestamp']).dt.date
                        if start_date:
                            logs_df = logs_df[logs_df['Date'] >= start_date]
                        if end_date:
                            logs_df = logs_df[logs_df['Date'] <= end_date]
                        logs_df = logs_df.drop('Date', axis=1)
                
                    if logs_df.empty:
                        st.warning("フィルター条件に一致するログが見つかりません。")
                        return
                
                    # 全選択チェックボックス
                    col1, col2 = st.columns([1, 11])
                    with col1:
                        select_all = st.checkbox("全選択")
                    
                    if select_all:
                        logs_df['選択'] = True
                    
                    # ログ一覧表示
                    edited_df = st.data_editor(
                        logs_df,
                        disabled=["ID", "User Question", "AI Answer", "Reference Document", "Timestamp"],
                        hide_index=True,
                        column_config={
                            "選択": st.column_config.CheckboxColumn(
                                "選択",
                                help="削除する行を選択",
                                default=False,
                            ),
                            "ID": st.column_config.NumberColumn(
                                "ID",
                                help="ログのID",
                                width="small",
                            ),
                            "User Question": st.column_config.TextColumn(
                                "ユーザーの質問",
                                help="ユーザーからの質問内容",
                                width="medium",
                            ),
                            "AI Answer": st.column_config.TextColumn(
                                "AIの回答",
                                help="AIからの回答内容",
                                width="large",
                            ),
                            "Reference Document": st.column_config.TextColumn(
                                "参考文書",
                                help="参照された文書",
                                width="medium",
                            ),
                            "Timestamp": st.column_config.TextColumn(
                                "生成時間",
                                help="ログが記録された日時",
                                width="medium",
                            ),
                        },
                        column_order=["選択", "ID", "User Question", "AI Answer", "Reference Document", "Timestamp"],
                        use_container_width=True,
                    )
                
                    # 削除ボタン
                    selected_rows = edited_df[edited_df['選択'] == True]
                    if not selected_rows.empty:
                        col1, col2, col3 = st.columns([4, 4, 4])
                        with col2:
                            if st.button(f"選択した {len(selected_rows)} 件のログを削除", type="primary"):
                                for _, row in selected_rows.iterrows():
                                    delete_log(row["ID"])
                                st.rerun()
                
                if __name__ == "__main__":
                    main()
            else:
                # 持久化された認証失敗を記録
                attempts = record_auth_failure()
                st.error("❌ パスワードが正しくありません。")

                if attempts >= 3:
                    st.error("🔒 セキュリティロックアウト: 3回連続でパスワードを間違えました。")
                    st.warning("⏰ 5分間（300秒）待ってから再試行してください。")
                    st.info("💡 ページを刷新してもロックアウト時間は変わりません。")
                else:
                    remaining_attempts = 3 - attempts
                    st.warning(f"⚠️ 残り試行回数: {remaining_attempts}回")

else:

    # CSSスタイルの追加
    st.markdown("""
        <style>
            .e10jh26i2 {
                display: none;
            }
            .stButton>button {
                width: 100%;
                margin-top: 10px;
            }
            .file-list {
                background-color: #f0f2f6;
                padding: 20px;
                border-radius: 10px;
                margin: 10px 0;
            }
            .upload-section {
                background-color: #e8f4ea;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
            }
            .delete-section {
                background-color: #fde8e8;
                padding: 20px;
                border-radius: 10px;
                margin: 20px 0;
            }
            .st-emotion-cache-yw8pof {
                max-width: 1200px;
            }
            .st-emotion-cache-yw8pof {
                padding-top: 60px;
            }
            [aria-label="Show password text"] {
                display: none !important;
            }
            .st-emotion-cache-1w723zb {
                max-width: 1200px !important;
                width: 100%;
                padding: 6rem 1rem 10rem;
            }
            /* データエディタのコンテナスタイル */
            .stDataFrame {
                width: 100% !important;
                max-width: 1200px !important;
            }
            /* データエディタのテーブルスタイル */
            .stDataFrame > div {
                width: 100% !important;
                max-width: 1200px !important;
            }
            /* チェックボックスを含むセルのスタイル */
            .stDataFrame [data-testid="stDataFrameCell"] {
                width: auto !important;
            }
            /* メインコンテナのスタイル */
            .main > div {
                max-width: 1200px !important;
                width: 100% !important;
            }
            /* タブコンテンツのスタイル */
            .stTabs [data-baseweb="tab-panel"] {
                max-width: 1200px !important;
                width: 100% !important;
            }
        </style>
    """, unsafe_allow_html=True)
    
    # 環境変数の読み込み    
    load_dotenv()
    
    # OpenAI API キーの確認
    if not os.environ.get("OPENAI_API_KEY"):
        st.error("OpenAI API キーが設定されていません。.envファイルを確認してください。")
    
    # 操作するフォルダのパスを指定
    FILE_DIRECTORY = "./files"
    
    # ディレクトリがなければ作成
    if not os.path.exists(FILE_DIRECTORY):
        os.makedirs(FILE_DIRECTORY)
    
    def list_files():
        """ファイル一覧を取得し、データフレームとして返す"""
        files = []
        jst = timezone(timedelta(hours=9))  # 日本標準時(JST)
        
        for file in os.listdir(FILE_DIRECTORY):
            file_path = os.path.join(FILE_DIRECTORY, file)
            size = os.path.getsize(file_path) / 1024  # KBに変換
            
            # タイムスタンプを取得してJSTに変換
            modified = datetime.fromtimestamp(os.path.getmtime(file_path))
            modified_jst = modified.astimezone(jst)
            
            files.append({
                'ダウンロード': False,  # 「ダウンロード」から「ダウンロード」に修正
                'ファイル名': file,
                'サイズ(KB)': f"{size:.1f}",
                '最終更新': modified_jst.strftime('%Y-%m-%d %H:%M')
            })
        return pd.DataFrame(files)
    
    def download_files(files_to_download):
        """選択されたファイルをダウンロードする関数"""
        try:
            if len(files_to_download) == 1:
                # 単一ファイルの場合
                file = files_to_download[0]
                file_path = os.path.join(FILE_DIRECTORY, file)
                with open(file_path, 'rb') as f:
                    file_content = f.read()
                st.download_button(
                    label=f"📥 {file} をダウンロード",
                    data=file_content,
                    file_name=file,
                    mime='text/plain'
                )
            else:
                # 複数ファイルの場合、ZIPファイルとして提供
                import zipfile
                import io
                
                # メモリ上でZIPファイルを作成
                zip_buffer = io.BytesIO()
                with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
                    for file in files_to_download:
                        file_path = os.path.join(FILE_DIRECTORY, file)
                        zip_file.write(file_path, file)
                
                # ZIPファイルをダウンロード
                st.download_button(
                    label=f"📥 選択した{len(files_to_download)}個のファイルをダウンロード",
                    data=zip_buffer.getvalue(),
                    file_name="selected_files.zip",
                    mime="application/zip"
                )
        except Exception as e:
            st.error(f"ファイルのダウンロード準備中にエラーが発生しました: {e}")
    
    def upload_files(uploaded_files):
        """複数ファイルをアップロード（セキュリティ検証付き）"""
        if not uploaded_files:  # アップロードされたファイルがない場合は処理をスキップ
            return

        success_count = 0
        error_messages = []
        security_warnings = []

        for uploaded_file in uploaded_files:
            try:
                # セキュリティ検証
                is_valid, error_msg, file_info = validate_file_upload(uploaded_file)

                if not is_valid:
                    error_messages.append(f"🚫 セキュリティ検証失敗 '{uploaded_file.name}': {error_msg}")
                    security_warnings.append(f"危険なファイル '{uploaded_file.name}' がブロックされました: {error_msg}")
                    continue

                # 安全なファイル名を生成
                safe_filename = get_safe_filename(uploaded_file.name)
                if safe_filename != uploaded_file.name:
                    st.info(f"ファイル名を安全な形式に変更しました: '{uploaded_file.name}' → '{safe_filename}'")

                # ファイル保存
                file_path = os.path.join(FILE_DIRECTORY, safe_filename)

                # 同名ファイルが存在する場合の処理
                if os.path.exists(file_path):
                    base_name, ext = os.path.splitext(safe_filename)
                    counter = 1
                    while os.path.exists(file_path):
                        safe_filename = f"{base_name}_{counter}{ext}"
                        file_path = os.path.join(FILE_DIRECTORY, safe_filename)
                        counter += 1
                    st.info(f"同名ファイルが存在するため、ファイル名を変更しました: {safe_filename}")

                # ファイル保存
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())

                success_count += 1
                st.success(f"✅ '{safe_filename}' が正常にアップロードされました（サイズ: {file_info.get('size', 0):,} bytes）")

            except Exception as e:
                error_messages.append(f"ファイル '{uploaded_file.name}' のアップロードに失敗しました: {e}")

        # セキュリティ警告の表示
        if security_warnings:
            st.error("🔒 セキュリティ警告:")
            for warning in security_warnings:
                st.error(f"• {warning}")

        # エラーメッセージの表示
        if error_messages:
            st.error("❌ エラー:")
            for error in error_messages:
                st.error(f"• {error}")

        if success_count > 0:
            st.success(f"📁 {success_count}個のファイルが安全にアップロードされました")
            # ファイル一覧の更新をトリガー
            st.session_state.refresh_files = True
            st.session_state.file_list_key = str(time.time())
            # アップロード完了後にファイル選択をクリア
            st.session_state.file_uploader = None
    
    def delete_files(files_to_delete):
        """複数ファイルを削除"""
        success_count = 0
        for file in files_to_delete:
            try:
                file_path = os.path.join(FILE_DIRECTORY, file)
                if os.path.exists(file_path):
                    os.remove(file_path)
                    success_count += 1
            except Exception as e:
                st.error(f"ファイル '{file}' の削除に失敗しました: {e}")
        
        if success_count > 0:
            st.success(f"{success_count}個のファイルが削除されました")
            # 削除後に選択状態をクリア
            st.session_state.temp_selected_files.clear()
            st.session_state.selected_files.clear()
            # ファイル一覧を更新
            st.session_state.file_list_key = str(time.time())
    
    def reload_files():
        """アプリケーションを再起動する関数"""
        try:
            # インデックスディレクトリを削除
            index_dir = "./vector_store"
            if os.path.exists(index_dir):
                shutil.rmtree(index_dir)
            
            # aichat.pyを再起動
            venv_python = os.getcwd() + '/venv/bin/python'
            subprocess.Popen([
                venv_python, 
                '-m', 
                'streamlit', 
                'run',
                'aichat.py',
                '--server.port=8501'
            ])
            
            st.success("ファイルを再読み込みしました")
            st.rerun()
            
        except Exception as e:
            st.error(f"再読み込み中にエラーが発生しました: {e}")
    
    def crawl_and_save_as_text(url, filename, max_pages=100, progress_callback=None):
        """URLからテキストを取得してファイルに保存する関数"""
        try:
            # crawler.pyの関数を呼び出す
            from crawler import crawl_and_save_as_text as crawler_func
            
            # 無視する拡張子のリストを定義
            ignored_extensions = [
                # 画像ファイル
                '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', 
                '.ico', '.svg', '.tiff', '.heic',
                # 文書ファイル
                '.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                # 圧縮ファイル
                '.zip', '.rar', '.7z', '.tar', '.gz',
                # 実行ファイル
                '.exe', '.dmg', '.app', '.apk',
                # メディアファイル
                '.mp3', '.mp4', '.wav', '.avi', '.mov', '.wmv',
                # その他
                '.csv', '.xml', '.json', '.sql', '.db'
            ]
            
            return crawler_func(
                url=url,
                filename=filename,
                max_pages=max_pages,
                progress_callback=progress_callback,
                ignored_extensions=ignored_extensions  # 無視する拡張子を渡す
            )
        except ImportError:
            st.error("crawler.pyが見つかりません")
            return False, None
        except Exception as e:
            st.error(f"クロール中にエラーが発生しました: {e}")
            return False, None
    
    def get_domain_from_url(url):
        """URLからドメイン名を抽出する関数"""
        try:
            parsed_url = urlparse(url)
            # ドメイン名から'www.'を除去し、'.'を'-'に置換
            domain = parsed_url.netloc.replace('www.', '').replace('.', '-')
            return domain
        except Exception:
            return "unknown-domain"
    
    def delete_old_domain_files(domain):
        """同じドメインの古いファイルを削除する関数"""
        try:
            files = os.listdir(FILE_DIRECTORY)
            domain_files = [f for f in files if f.startswith(domain + "_")]
            
            # 日付でソート（新しい順）
            domain_files.sort(reverse=True)
            
            # 最新以外を削除
            if len(domain_files) > 1:
                for old_file in domain_files[1:]:
                    file_path = os.path.join(FILE_DIRECTORY, old_file)
                    os.remove(file_path)
                return len(domain_files) - 1
            return 0
        except Exception as e:
            st.error(f"古いファイルの削除中にエラーが発生しました: {e}")
            return 0
    
    def save_env_var(key, value):
        """環境変数を.envファイルに保存する"""
        env_path = '.env'
        set_key(env_path, key, value)
        os.environ[key] = value
    
    def load_api_settings():
        """APIの設定を読み込む"""
        load_dotenv(override=True)  # 既存の環境変数を上書き
        
        # プロバイダー設定
        active_provider = os.getenv('ACTIVE_PROVIDER', 'openai')
        embedding_provider = os.getenv('EMBEDDING_PROVIDER', 'openai')
        
        # 各プロバイダーの設定を読み込む
        settings = {
            # プロバイダー設定
            'ACTIVE_PROVIDER': active_provider,
            'EMBEDDING_PROVIDER': embedding_provider,
            
            # OpenAI設定
            'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY', ''),
            'EMBEDDING_MODEL': os.getenv('EMBEDDING_MODEL', 'text-embedding-3-small'),
            'CHAT_MODEL': os.getenv('CHAT_MODEL', 'gpt-4o-mini'),
            
            # Deepseek設定
            'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', ''),
            'DEEPSEEK_MODEL': os.getenv('DEEPSEEK_MODEL', 'deepseek-chat'),
            
            # Ollama設定
            'OLLAMA_BASE_URL': os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434'),
            'OLLAMA_EMBED_MODEL': os.getenv('OLLAMA_EMBED_MODEL', 'llama2'),
            'OLLAMA_CHAT_MODEL': os.getenv('OLLAMA_CHAT_MODEL', 'llama2'),
        }
        
        return settings
    
    def get_ollama_models(base_url):
        """Ollamaサーバーから利用可能なモデル一覧を取得"""
        try:
            response = requests.get(f"{base_url}/api/tags")
            if response.status_code == 200:
                models = response.json()
                return [model['name'] for model in models['models']]
            return ["llama2"]  # デフォルトモデル
        except Exception as e:
            st.warning(f"Ollamaサーバーからモデル一覧を取得できませんでした: {e}")
            return ["llama2"]  # エラー時はデフォルトモデルを返す
    
    def get_openai_embedding_models(api_key):
        """OpenAI APIから利用可能なEmbeddingモデル一覧を取得する関数"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            response = requests.get("https://api.openai.com/v1/models", headers=headers)
            if response.status_code == 200:
                models = response.json()
                # Embeddingモデルのみをフィルタリング
                embedding_models = [
                    model['id'] for model in models['data']
                    if model['id'].startswith('text-embedding-')
                ]
                # モデルを名前でソート
                embedding_models.sort()
                return embedding_models
            return ["text-embedding-3-small"]  # デフォルトモデル
        except Exception as e:
            st.warning(f"OpenAI Embeddingモデル一覧の取得に失敗しました: {e}")
            return ["text-embedding-3-small"]  # エラー時はデフォルトモデルを返す
    
    def get_openai_models(api_key):
        """OpenAI APIから利用可能なモデル一覧を取得する関数"""
        try:
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            response = requests.get("https://api.openai.com/v1/models", headers=headers)
            if response.status_code == 200:
                models = response.json()
                # チャットモデルのみをフィルタリング
                chat_models = [
                    model['id'] for model in models['data']
                    if model['id'].startswith(('gpt-3.5', 'gpt-4'))
                ]
                # モデルを名前でソート
                chat_models.sort()
                return chat_models
            return ["gpt-4o-mini"]  # デフォルトモデル
        except Exception as e:
            st.warning(f"OpenAIモデル一覧の取得に失敗しました: {e}")
            return ["gpt-4o-mini"]  # エラー時はデフォルトモデルを返す
    
    def get_gemini_models(api_key):
        """Gemini APIから利用可能なチャットモデル一覧を取得する関数"""
        try:
            from google import genai
            if api_key:
                client = genai.Client(api_key=api_key)
            else:
                client = genai.Client()  # 環境変数 GAI_API_KEY などに設定済みの場合

            chat_models = []
            for m in client.models.list():
                if "generateContent" in m.supported_actions:
                    model_name = m.name.replace("models/", "")
                    chat_models.append(model_name)

            chat_models.sort()
            return chat_models if chat_models else ["gemini-2.0-flash"]  # モデルが空ならデフォルト

        except Exception as e:
            st.warning(f"Geminiモデル一覧の取得に失敗しました: {e}")
            return ["gemini-2.0-flash"]  # エラー時はデフォルトモデルを返す

    def display_api_settings():
        """API設定画面を表示する"""
        settings = load_api_settings()
        
        st.subheader("🔑 API設定")
        
        # API設定状態を管理
        if 'api_change_triggered' not in st.session_state:
            st.session_state.api_change_triggered = False
        if 'api_form_key' not in st.session_state:
            st.session_state.api_form_key = str(time.time())
        if 'api_change_success' not in st.session_state:
            st.session_state.api_change_success = False
        
        # --- NEW: Initialize session state from .env on first load ---
        if "ACTIVE_PROVIDER" not in st.session_state:
            st.session_state["ACTIVE_PROVIDER"] = os.getenv("ACTIVE_PROVIDER", settings['ACTIVE_PROVIDER'])
        if "chat_model" not in st.session_state:
            st.session_state["chat_model"] = os.getenv("CHAT_MODEL", settings['CHAT_MODEL'])
        if "gemini_model" not in st.session_state:
            st.session_state.gemini_model = os.getenv("GEMINI_MODEL", settings.get('GEMINI_MODEL', 'gemini-2.5-pro-preview-06-05'))
        # ------------------------------------------------------------

        # フォームの状態を保持
        if 'openai_api_key' not in st.session_state:
            st.session_state.openai_api_key = settings['OPENAI_API_KEY']
        if 'embedding_model' not in st.session_state:
            st.session_state.embedding_model = settings['EMBEDDING_MODEL']
        if 'chat_model' not in st.session_state:
            st.session_state.chat_model = settings['CHAT_MODEL']
        
        # API設定フォームをコンテナで囲む
        with st.form(key=f"api_form_{st.session_state.api_form_key}", clear_on_submit=True):
            # Embedding設定
            st.markdown("### Embedding設定")
            embed_provider = st.radio(
                "使用するEmbeddingモデル",
                options=["OpenAI"],
                index=0 if settings['EMBEDDING_PROVIDER'] == 'openai' else 1,
                horizontal=True
            )
            
            if embed_provider == "OpenAI":
                openai_api_key = st.text_input(
                    "OpenAI API Key",
                    value=st.session_state.openai_api_key,
                    type="password",
                    help="Embeddingに必要なOpenAI APIキー"
                )
                
                # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                if openai_api_key:
                    try:
                        embedding_models = get_openai_embedding_models(openai_api_key)
                        # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                        current_model = st.session_state.embedding_model if st.session_state.embedding_model in embedding_models else "text-embedding-3-small"
                        embedding_model = st.selectbox(
                            "Embeddingモデル",
                            options=embedding_models,
                            index=embedding_models.index(current_model)
                        )
                    except Exception as e:
                        st.error(f"Embeddingモデル一覧の取得に失敗しました: {e}")
                        embedding_model = st.session_state.embedding_model
                else:
                    # APIキーが入力されていない場合は、現在の設定値を使用
                    embedding_model = st.session_state.embedding_model
                    st.warning("OpenAI APIキーを入力すると、利用可能なEmbeddingモデル一覧が表示されます。")
            
            st.markdown("---")
            
            # チャットモデルプロバイダーの選択
            st.markdown("### チャットモデル設定")
            # 触发切换时刷新
            if "ACTIVE_PROVIDER" not in st.session_state:
                st.session_state["ACTIVE_PROVIDER"] = "openai"

            chat_provider = st.radio(
                "使用するチャットモデル",
                options=["OpenAI", "Gemini"],
                index={"openai": 0, "gemini": 1}[st.session_state["ACTIVE_PROVIDER"].lower()],
                horizontal=True,
                key="chat_provider"  # 把选择绑定到 session_state["chat_provider"]
            )

            # 如果切换了 provider，触发 rerun
            if chat_provider != st.session_state.get("ACTIVE_PROVIDER"):
                st.session_state["ACTIVE_PROVIDER"] = chat_provider
                st.session_state["chat_model"] = None
                st.rerun()

            if st.session_state["ACTIVE_PROVIDER"] == "OpenAI":
                if embed_provider != "OpenAI":
                    openai_api_key = st.text_input(
                        "OpenAI API Key",
                        value=st.session_state.openai_api_key,
                        type="password"
                    )
                
                # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                if openai_api_key:
                    try:
                        chat_models = get_openai_models(openai_api_key)
                        # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                        current_model = st.session_state.chat_model if st.session_state.chat_model in chat_models else "gpt-4o-mini"
                        chat_model = st.selectbox(
                            "OpenAIチャットモデル",
                            options=chat_models,
                            index=chat_models.index(current_model)
                        )
                    except Exception as e:
                        st.error(f"モデル一覧の取得に失敗しました: {e}")
                        chat_model = st.session_state.chat_model
                else:
                    # APIキーが入力されていない場合は、現在の設定値を使用
                    chat_model = st.session_state.chat_model
                    st.warning("OpenAI APIキーを入力すると、利用可能なモデル一覧が表示されます。")

            if st.session_state["ACTIVE_PROVIDER"] == "Gemini":
                            if embed_provider != "Gemini":
                                gemini_api_key = st.text_input(
                                    "Gemini API Key",
                                    value=st.session_state.gemini_api_key,
                                    type="password"
                                )
                            
                            # OpenAI APIキーが入力されている場合のみモデル一覧を取得
                            if gemini_api_key:
                                try:
                                    chat_models = get_gemini_models(gemini_api_key)
                                    # 現在選択されているモデルが利用可能なモデル一覧にない場合は、デフォルトモデルを使用
                                    current_model = st.session_state.gemini_model if st.session_state.gemini_model in chat_models else "gemini-2.5-pro-preview-06-05"
                                    chat_model = st.selectbox(
                                        "Geminiチャットモデル",
                                        options=chat_models,
                                        index=chat_models.index(current_model)
                                    )
                                except Exception as e:
                                    st.error(f"モデル一覧の取得に失敗しました: {e}")
                                    chat_model = st.session_state.chat_model
                            else:
                                # APIキーが入力されていない場合は、現在の設定値を使用
                                chat_model = st.session_state.chat_model
                                st.warning("Gemini APIキーを入力すると、利用可能なモデル一覧が表示されます。")
                
            # 設定の保存ボタン
            submitted = st.form_submit_button(
                "設定を保存",
                type="primary",
                disabled=st.session_state.api_change_triggered
            )
                
            if submitted:
                try:
                    if openai_api_key:
                        save_env_var('OPENAI_API_KEY', openai_api_key)
                        st.session_state.openai_api_key = openai_api_key
                    if embed_provider == "OpenAI":
                        save_env_var('EMBEDDING_MODEL', embedding_model)
                        save_env_var('EMBEDDING_PROVIDER', 'openai')
                        st.session_state.embedding_model = embedding_model
                    if chat_provider == "OpenAI":
                        save_env_var('CHAT_MODEL', chat_model)
                        st.session_state["chat_model"] = chat_model
                        st.session_state["ACTIVE_PROVIDER"] = "openai"
                        save_env_var('ACTIVE_PROVIDER', "openai")
                    if chat_provider == "Gemini":
                        save_env_var('GEMINI_MODEL', chat_model)
                        st.session_state["chat_model"] = chat_model
                        st.session_state["ACTIVE_PROVIDER"] = "gemini"
                        save_env_var('ACTIVE_PROVIDER', "gemini")
                    
                    # --- NEW: Reload from .env and update session state ---
                    load_dotenv(override=True)
                    st.session_state["ACTIVE_PROVIDER"] = os.getenv("ACTIVE_PROVIDER", st.session_state.get("ACTIVE_PROVIDER", "openai"))
                    st.session_state.gemini_model = os.getenv("GEMINI_MODEL", st.session_state.get("gemini_model", "gemini-2.5-pro-preview-06-05"))
                    # ------------------------------------------------------

                    # API変更処理中であることを記録
                    st.session_state.api_change_triggered = True
                        
                    # 成功状態を設定
                    st.session_state.api_change_success = True
                        
                    # フォームをリセット
                    st.session_state.api_change_triggered = False
                    st.session_state.api_form_key = str(time.time())
                    
                    reload_files()

                    # 画面を更新
                    st.rerun()
                except Exception as e:
                    st.error(f"API設定の保存中にエラーが発生しました: {e}")
                    st.session_state.api_change_triggered = False
    
    def load_parameter_settings():
        """パラメータ設定を読み込む"""
        load_dotenv(override=True)
        
        settings = {
            'CHUNK_SIZE': int(os.getenv('CHUNK_SIZE', '500')),
            'CHUNK_OVERLAP': int(os.getenv('CHUNK_OVERLAP', '50')),
            'SYSTEM_PROMPT': os.getenv('SYSTEM_PROMPT', """あなたは丁寧で正確な回答を提供するアシスタントです。
以下の指示に従って回答してください：
1. 提供された文脈を参考に、日本語で質問に対して具体的に回答してください
2. 提供された文脈に無い情報は提供せず、「申し訳ございません、その質問にはお答えすることができません。」のみ回答してください
3. 参考にした文脈の引用は回答しないでください
4. 必要に応じて、参考にした文書の内容を引用してください
5. 質問の意図を理解し、関連する情報があれば柔軟に解釈して回答してください
6. 文脈内の情報が部分的にでも質問に関連する場合は、その情報を活用して回答してください""")
        }
        return settings
    
    def display_parameter_settings():
        """パラメータ設定画面を表示する"""
        settings = load_parameter_settings()
        
        st.subheader("⚙️ パラメータ設定")
        
        # パラメータ設定状態を管理
        if 'parameter_change_triggered' not in st.session_state:
            st.session_state.parameter_change_triggered = False
        if 'parameter_form_key' not in st.session_state:
            st.session_state.parameter_form_key = str(time.time())
        if 'parameter_change_success' not in st.session_state:
            st.session_state.parameter_change_success = False
        
        # フォームの状態を保持
        if 'chunk_size' not in st.session_state:
            st.session_state.chunk_size = settings['CHUNK_SIZE']
        if 'chunk_overlap' not in st.session_state:
            st.session_state.chunk_overlap = settings['CHUNK_OVERLAP']
        if 'system_prompt' not in st.session_state:
            st.session_state.system_prompt = settings['SYSTEM_PROMPT']
        
        # パラメータ設定フォームをコンテナで囲む
        with st.form(key=f"parameter_form_{st.session_state.parameter_form_key}", clear_on_submit=True):
            # チャンクサイズ設定
            st.markdown("### テキスト分割設定")
            col1, col2 = st.columns(2)
            
            with col1:
                chunk_size = st.number_input(
                    "チャンクサイズ",
                    min_value=100,
                    max_value=2000,
                    value=st.session_state.chunk_size,
                    step=50,
                    help="テキストを分割する際の1チャンクあたりの文字数。大きすぎるとトークン制限エラーが発生する可能性があります。"
                )
            
            with col2:
                chunk_overlap = st.number_input(
                    "オーバーラップ",
                    min_value=0,
                    max_value=500,
                    value=st.session_state.chunk_overlap,
                    step=10,
                    help="チャンク間で重複させる文字数。文脈の連続性を保つために使用されます。"
                )
            
            # システムプロンプト設定
            st.markdown("### システムプロンプト設定")
            system_prompt = st.text_area(
                "システムプロンプト",
                value=st.session_state.system_prompt,
                height=300,
                help="AIアシスタントの振る舞いを定義するプロンプト。回答の形式や制約を指定できます。"
            )
            
            # 設定の保存ボタン
            submitted = st.form_submit_button(
                "設定を保存",
                type="primary",
                disabled=st.session_state.parameter_change_triggered
            )
            
            if submitted:
                try:
                    # 環境変数の保存
                    save_env_var('CHUNK_SIZE', str(chunk_size))
                    save_env_var('CHUNK_OVERLAP', str(chunk_overlap))
                    save_env_var('SYSTEM_PROMPT', system_prompt)
                    
                    # 状態を更新
                    st.session_state.chunk_size = chunk_size
                    st.session_state.chunk_overlap = chunk_overlap
                    st.session_state.system_prompt = system_prompt
                    
                    # パラメータ変更処理中であることを記録
                    st.session_state.parameter_change_triggered = True
                    
                    # 成功状態を設定
                    st.session_state.parameter_change_success = True
                    
                    # フォームをリセット
                    st.session_state.parameter_change_triggered = False
                    st.session_state.parameter_form_key = str(time.time())
                    
                    # 画面を更新
                    st.rerun()
                    
                    # ベクトルストアの再構築が必要な場合は警告を表示
                    if (chunk_size != settings['CHUNK_SIZE'] or 
                        chunk_overlap != settings['CHUNK_OVERLAP']):
                        st.warning("""
                            チャンクサイズまたはオーバーラップを変更しました。
                            新しい設定を反映するには、ベクトルストアを再構築する必要があります。
                            以下の手順で再構築してください：
                            1. ファイル管理タブで「ファイルを再読み込み」を実行
                            2. または、vector_storeディレクトリを手動で削除
                        """)
                    
                except Exception as e:
                    st.error(f"設定の保存中にエラーが発生しました: {e}")
                    st.session_state.parameter_change_triggered = False
        
        # パスワード変更が完了したら、入力フィールドをリセット
        if st.session_state.password_change_triggered:
            if st.button("新しいパスワードを設定", type="secondary"):
                st.session_state.password_change_triggered = False
                st.session_state.password_form_key = str(time.time())
                st.rerun()
    
    def display_password_settings():
        """パスワード設定画面を表示する関数"""
        global stored_password_hash
        
        st.subheader("🔒 パスワード設定")
        
        # パスワード入力状態を管理
        if 'password_change_triggered' not in st.session_state:
            st.session_state.password_change_triggered = False
        if 'password_form_key' not in st.session_state:
            st.session_state.password_form_key = str(time.time())
        
        # フォームの状態を保持
        if 'current_password' not in st.session_state:
            st.session_state.current_password = ""
        if 'new_password' not in st.session_state:
            st.session_state.new_password = ""
        if 'confirm_password' not in st.session_state:
            st.session_state.confirm_password = ""
        
        # パスワード入力フォームをコンテナで囲む
        with st.form(key=f"password_form_{st.session_state.password_form_key}", clear_on_submit=True):
            # 現在のパスワード確認
            current_password = st.text_input(
                "現在のパスワード",
                type="password",
                key="current_password_input",
                disabled=st.session_state.password_change_triggered,
                value=st.session_state.current_password
            )
            
            # 新しいパスワード入力
            new_password = st.text_input(
                "新しいパスワード",
                type="password",
                key="new_password_input",
                disabled=st.session_state.password_change_triggered,
                value=st.session_state.new_password
            )
            
            # 新しいパスワード確認
            confirm_password = st.text_input(
                "新しいパスワード（確認）",
                type="password",
                key="confirm_password_input",
                disabled=st.session_state.password_change_triggered,
                value=st.session_state.confirm_password
            )
            
            # パスワード変更ボタン
            submitted = st.form_submit_button(
                "パスワードを変更",
                type="primary",
                disabled=st.session_state.password_change_triggered
            )
            
            if submitted:
                try:
                    if not current_password or not new_password or not confirm_password:
                        st.error("すべての項目を入力してください。")
                        return
                    
                    # 現在のパスワードの確認
                    current_password_hash = hashlib.sha256(current_password.encode()).hexdigest()
                    if current_password_hash != stored_password_hash:
                        st.error("現在のパスワードが正しくありません。")
                        return
                    
                    # 新しいパスワードの確認
                    if new_password != confirm_password:
                        st.error("新しいパスワードが一致しません。")
                        return
                    
                    # パスワードの長さチェック
                    if len(new_password) < 8:
                        st.error("パスワードは8文字以上である必要があります。")
                        return
                    
                    # パスワード変更処理中であることを記録
                    st.session_state.password_change_triggered = True
                    
                    # 環境変数の更新
                    save_env_var('ADMIN_PASSWORD', new_password)
                    
                    # グローバル変数の更新
                    stored_password_hash = hashlib.sha256(new_password.encode()).hexdigest()
                    
                    # 入力値をクリア
                    st.session_state.current_password = ""
                    st.session_state.new_password = ""
                    st.session_state.confirm_password = ""
                    
                    # 成功状態を設定
                    st.session_state.password_change_success = True
                    
                    # フォームをリセット
                    st.session_state.password_change_triggered = False
                    st.session_state.password_form_key = str(time.time())
                    
                    # 画面を更新
                    st.rerun()
                except Exception as e:
                    st.error(f"パスワードの変更中にエラーが発生しました: {e}")
                    st.session_state.password_change_triggered = False
        
        # パスワード変更が完了したら、入力フィールドをリセット
        if st.session_state.password_change_triggered:
            if st.button("新しいパスワードを設定", type="secondary"):
                st.session_state.password_change_triggered = False
                st.session_state.password_form_key = str(time.time())
                st.rerun()
    
    def main():
        st.title("📁 ファイル管理システム")
        st.session_state.setdefault("password_change_triggered", False)
        
        # パスワード変更成功メッセージ用のコンテナ
        if 'password_change_success' in st.session_state and st.session_state.password_change_success:
            success_container = st.empty()
            success_container.success("パスワードを変更しました。")
            # 3秒後にメッセージを消す
            time.sleep(3)
            success_container.empty()
            # 成功状態をリセット
            st.session_state.password_change_success = False
            st.rerun()
        
        # パラメータ設定成功メッセージ用のコンテナ
        if 'parameter_change_success' in st.session_state and st.session_state.parameter_change_success:
            success_container = st.empty()
            success_container.success("パラメータ設定を保存しました。")
            # 3秒後にメッセージを消す
            time.sleep(3)
            success_container.empty()
            # 成功状態をリセット
            st.session_state.parameter_change_success = False
            st.rerun()
        
        # API設定成功メッセージ用のコンテナ
        if 'api_change_success' in st.session_state and st.session_state.api_change_success:
            success_container = st.empty()
            success_container.success("API設定を保存しました。")
            # 3秒後にメッセージを消す
            time.sleep(3)
            success_container.empty()
            # 成功状態をリセット
            st.session_state.api_change_success = False
            st.rerun()
        
        # タブを作成
        tab1, tab2, tab3, tab4, tab5 = st.tabs(["ファイル管理", "チャット履歴", "API設定", "パラメータ設定", "パスワード設定"])
        
        with tab1:
            # ファイル一覧用のコンテナを作成
            file_list_container = st.empty()
            
            def update_file_list():
                """ファイル一覧を更新する関数"""
                with file_list_container.container():
                    st.subheader("📋 ファイル一覧")
                    files_df = list_files()
                    if not files_df.empty:
                        current_files = set(files_df['ファイル名'].tolist())
                        
                        # DataFrameの「選択」列をsession_stateに合わせて更新
                        files_df['選択'] = files_df['ファイル名'].isin(st.session_state.temp_selected_files)

                        # DataEditorで編集
                        editor_key = f"file_list_editor_{st.session_state.file_list_key}"
                        edited_df = st.data_editor(
                            files_df,
                            disabled=["ファイル名", "サイズ(KB)", "最終更新"],
                            hide_index=True,
                            column_config={
                                "選択": st.column_config.CheckboxColumn(
                                    "選択",
                                    help="ダウンロードまたは削除するファイルを選択",
                                    default=False,
                                    required=True,
                                ),
                                "ファイル名": st.column_config.TextColumn(
                                    "ファイル名",
                                    help="ファイルの名前",
                                    width="large",
                                ),
                                "サイズ(KB)": st.column_config.TextColumn(
                                    "サイズ(KB)",
                                    help="ファイルサイズ（キロバイト）",
                                    width="medium",
                                ),
                                "最終更新": st.column_config.TextColumn(
                                    "最終更新",
                                    help="ファイルの最終更新日時",
                                    width="medium",
                                ),
                            },
                            column_order=["選択", "ファイル名", "サイズ(KB)", "最終更新"],
                            use_container_width=True,
                            key=editor_key
                        )

                        # 編集後の選択状態を更新
                        if edited_df is not None:
                            selected_files = set(edited_df[edited_df['選択']]['ファイル名'].tolist())
                            if selected_files != st.session_state.temp_selected_files:
                                st.session_state.temp_selected_files = selected_files
                                st.rerun()

                        if st.session_state.temp_selected_files:
                            st.markdown("<br>", unsafe_allow_html=True)
                            col1, col2, col3 = st.columns([1, 2, 1])
                            with col2:
                                download_key = f"download_files_button_{st.session_state.file_list_key}"
                                if st.button(
                                    f"📥 選択した{len(st.session_state.temp_selected_files)}個のファイルをダウンロード",
                                    type="primary",
                                    key=download_key
                                ):
                                    selected_files = list(st.session_state.temp_selected_files)
                                    download_files(selected_files)
                                delete_key = f"delete_files_button_{st.session_state.file_list_key}"
                                if st.button(
                                    f"🗑️ 選択した{len(st.session_state.temp_selected_files)}個のファイルを削除",
                                    type="primary",
                                    key=delete_key
                                ):
                                    selected_files = list(st.session_state.temp_selected_files)
                                    delete_files(selected_files)
                                    st.session_state.temp_selected_files.clear()
                                    st.session_state.selected_files.clear()
                                    st.session_state.file_list_key = str(time.time())
                                    st.rerun()
                    else:
                        st.info("ファイルが存在しません")
                        st.session_state.temp_selected_files.clear()
                        st.session_state.selected_files.clear()
                
            # 初期表示
            update_file_list()
            
            # アップロードセクション
            st.markdown("---")
            with st.expander("📤 ファイルのアップロード", expanded=True):
                
                # ファイルアップローダーの表示
                uploaded_files = st.file_uploader(
                    "ファイルを選択してください（複数可）(ファイル名は英数字に変換して下さい)",
                    accept_multiple_files=True,
                    type=["txt", "pdf", "csv", "xlsx", "xls"],
                    key=f"uploader_{st.session_state.upload_state['uploader_key']}"
                )
                
                # ファイルが選択された場合の処理
                if uploaded_files and not st.session_state.upload_state['is_complete']:
                    success_count = 0
                    error_messages = []
                    security_warnings = []

                    # ファイルのアップロード処理（セキュリティ検証付き）
                    for uploaded_file in uploaded_files:
                        try:
                            # セキュリティ検証
                            is_valid, error_msg, file_info = validate_file_upload(uploaded_file)

                            if not is_valid:
                                error_messages.append(f"🚫 セキュリティ検証失敗 '{uploaded_file.name}': {error_msg}")
                                security_warnings.append(f"危険なファイル '{uploaded_file.name}' がブロックされました: {error_msg}")
                                continue

                            # 安全なファイル名を生成
                            safe_filename = get_safe_filename(uploaded_file.name)
                            if safe_filename != uploaded_file.name:
                                st.info(f"ファイル名を安全な形式に変更しました: '{uploaded_file.name}' → '{safe_filename}'")

                            # ファイル保存
                            file_path = os.path.join(FILE_DIRECTORY, safe_filename)

                            # 同名ファイルが存在する場合の処理
                            if os.path.exists(file_path):
                                base_name, ext = os.path.splitext(safe_filename)
                                counter = 1
                                while os.path.exists(file_path):
                                    safe_filename = f"{base_name}_{counter}{ext}"
                                    file_path = os.path.join(FILE_DIRECTORY, safe_filename)
                                    counter += 1
                                st.info(f"同名ファイルが存在するため、ファイル名を変更しました: {safe_filename}")

                            # ファイル保存
                            with open(file_path, "wb") as f:
                                f.write(uploaded_file.getbuffer())

                            success_count += 1
                            st.success(f"✅ '{safe_filename}' が正常にアップロードされました（サイズ: {file_info.get('size', 0):,} bytes）")

                        except Exception as e:
                            error_messages.append(f"ファイル '{uploaded_file.name}' のアップロードに失敗しました: {e}")

                    # セキュリティ警告の表示
                    if security_warnings:
                        st.error("🔒 セキュリティ警告:")
                        for warning in security_warnings:
                            st.error(f"• {warning}")

                    # 結果の表示
                    if success_count > 0:
                        st.success(f"📁 {success_count}個のファイルが安全にアップロードされました")
                        # ファイル一覧の更新をトリガー
                        st.session_state.file_list_key = str(time.time())
                        # アップロード状態を更新
                        st.session_state.upload_state['is_complete'] = True
                        st.session_state.upload_state['uploader_key'] = str(time.time())
                        # 画面を更新
                        st.rerun()
                    elif not error_messages and not security_warnings:
                        st.warning("アップロードするファイルが選択されていません")

                    # エラーメッセージの表示
                    if error_messages:
                        st.error("❌ エラー:")
                        for error in error_messages:
                            st.error(f"• {error}")
                
                # ファイルが選択されていない場合はアップロード状態をリセット
                elif not uploaded_files:
                    st.session_state.upload_state['is_complete'] = False
                
            # クローリングセクション
            st.markdown("---")
            with st.expander("🌐 ウェブサイトから学習データを追加", expanded=True):
                # 複数URLの入力を許可
                urls_input = st.text_area(
                    "クロールするURLを入力してください（1行に1つのURL）",
                    height=100,
                    help="複数のURLを入力する場合は、1行に1つのURLを入力してください"
                )
                
                if urls_input:
                    # 入力されたURLを行で分割してリスト化
                    urls = [url.strip() for url in urls_input.split('\n') if url.strip()]
                    
                    # 各URLのドメインとファイル名を生成
                    url_files = []
                    for url in urls:
                        if not url.startswith(('http://', 'https://')):
                            url = 'https://' + url
                        domain = get_domain_from_url(url)
                        current_date = datetime.now().strftime('%Y%m%d')
                        filename = f"{domain}_{current_date}.txt"
                        url_files.append((url, filename))
                    
                    # クロール開始ボタン
                    if st.button("クロールしてテキスト化", disabled=not urls):
                        # 進捗表示用のコンテナを作成
                        progress_container = st.empty()
                        status_container = st.empty()
                        result_container = st.empty()
                        
                        def update_progress(progress, status):
                            if progress is not None:
                                progress_container.progress(progress)
                            if status:
                                status_container.text(status)
                        
                        # 全体の進捗を計算するための変数
                        total_urls = len(urls)
                        success_count = 0
                        
                        for i, (url, filename) in enumerate(url_files):
                            # 個別のURLの進捗を全体の進捗に変換するラッパー関数
                            def progress_wrapper(progress, status):
                                if progress is not None:
                                    overall_progress = (i + progress) / total_urls
                                    update_progress(overall_progress, f"URL {i+1}/{total_urls}: {status}")
                                else:
                                    update_progress(None, status)
                            
                            success, output_filename = crawl_and_save_as_text(
                                url, 
                                filename, 
                                max_pages=100, 
                                progress_callback=progress_wrapper
                            )
                            
                            if success:
                                success_count += 1
                                # 同じドメインの古いファイルを削除
                                domain = get_domain_from_url(url)
                                deleted_count = delete_old_domain_files(domain)
                        
                        # 最終的な結果を表示（3秒後に消える）
                        success_message = f"{success_count}/{total_urls}のURLのクロールが完了しました。"
                        result_container.success(success_message)
                        
                        # 進捗表示をクリア
                        progress_container.empty()
                        status_container.empty()
                        
                        # ファイル一覧のキーを更新してから表示を更新
                        st.session_state.file_list_key = str(time.time())
                        update_file_list()  # ファイル一覧を更新
                        
                        # 3秒後に結果メッセージを消す
                        time.sleep(3)
                        result_container.empty()
        
        with tab2:
            display_chat_logs()  # チャット履歴表示機能
        
        with tab3:
            display_api_settings()  # API設定表示機能
        
        with tab4:
            display_parameter_settings()  # パラメータ設定表示機能
        
        with tab5:
            display_password_settings()  # パスワード設定表示機能
        
    def display_chat_logs():
        """チャット履歴を表示する関数"""
        DATABASE = 'aichat_log.db'
    
        # DBコネクション取得関数
        def get_db_connection():
            conn = sqlite3.connect(DATABASE)
            conn.row_factory = sqlite3.Row
            return conn
    
        # テーブル作成関数
        def create_logs_table():
            conn = get_db_connection()
            try:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_question TEXT NOT NULL,
                        ai_answer TEXT NOT NULL,
                        reference_document TEXT,
                        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                conn.commit()
            except Exception as e:
                st.error(f"テーブル作成中にエラーが発生しました: {e}")
            finally:
                conn.close()
    
        # ログ削除関数
        def delete_log(log_id):
            conn = get_db_connection()
            conn.execute('DELETE FROM logs WHERE id = ?', (log_id,))
            conn.commit()
            conn.close()
            st.success(f"Log with ID {log_id} deleted successfully!")
            st.rerun()
    
        # テーブルが存在しない場合は作成
        create_logs_table()
    
        # DBからログを取得
        conn = get_db_connection()
        logs = conn.execute("SELECT id, user_question, ai_answer, reference_document, timestamp FROM logs").fetchall()
        conn.close()
    
        # DataFrameに変換
        logs_df = pd.DataFrame(logs, columns=['ID', 'User Question', 'AI Answer', 'Reference Document', 'Timestamp'])
        logs_df['選択'] = False
    
        if logs_df.empty:
            st.warning("チャット履歴が存在しません")
            return
    
        # フィルターセクション
        col1, col2, col3 = st.columns([2, 1, 1])
        with col1:
            search_query = st.text_input("テキスト検索", "")
        with col2:
            start_date = st.date_input("開始日", value=None, key="start_date")
        with col3:
            end_date = st.date_input("終了日", value=None, key="end_date")
    
        # フィルター適用
        if search_query:
            mask = (
                logs_df['User Question'].str.contains(search_query, case=False, na=False) |
                logs_df['AI Answer'].str.contains(search_query, case=False, na=False) |
                logs_df['Reference Document'].str.contains(search_query, case=False, na=False)
            )
            logs_df = logs_df[mask]
    
        # 日付フィルター
        if start_date or end_date:
            logs_df['Date'] = pd.to_datetime(logs_df['Timestamp']).dt.date
            if start_date:
                logs_df = logs_df[logs_df['Date'] >= start_date]
            if end_date:
                logs_df = logs_df[logs_df['Date'] <= end_date]
            logs_df = logs_df.drop('Date', axis=1)
    
        if logs_df.empty:
            st.warning("フィルター条件に一致するログが見つかりません。")
            return
    
        # 全選択チェックボックス
        col1, col2 = st.columns([1, 11])
        with col1:
            select_all = st.checkbox("全選択")
    
        if select_all:
            logs_df['選択'] = True
    
        # ログ一覧表示
        edited_df = st.data_editor(
            logs_df,
            disabled=["ID", "User Question", "AI Answer", "Reference Document", "Timestamp"],
            hide_index=True,
            column_config={
                "選択": st.column_config.CheckboxColumn(
                    "選択",
                    help="削除する行を選択",
                    default=False,
                ),
                "ID": st.column_config.NumberColumn(
                    "ID",
                    help="ログのID",
                    width="small",
                ),
                "User Question": st.column_config.TextColumn(
                    "ユーザーの質問",
                    help="ユーザーからの質問内容",
                    width="medium",
                ),
                "AI Answer": st.column_config.TextColumn(
                    "AIの回答",
                    help="AIからの回答内容",
                    width="large",
                ),
                "Reference Document": st.column_config.TextColumn(
                    "参考文書",
                    help="参照された文書",
                    width="medium",
                ),
                "Timestamp": st.column_config.TextColumn(
                    "生成時間",
                    help="ログが記録された日時",
                    width="medium",
                ),
            },
            column_order=["選択", "ID", "User Question", "AI Answer", "Reference Document", "Timestamp"],
            use_container_width=True,
        )
    
        # 削除ボタン
        selected_rows = edited_df[edited_df['選択'] == True]
        if not selected_rows.empty:
            col1, col2, col3 = st.columns([4, 4, 4])
            with col2:
                if st.button(f"選択した {len(selected_rows)} 件のログを削除", type="primary"):
                    for _, row in selected_rows.iterrows():
                        delete_log(row["ID"])
                    st.rerun()
    
    if __name__ == "__main__":
        main()
