"""
文件上传安全验证模块
防止双扩展名绕过攻击和其他文件上传安全漏洞
"""

import os
import mimetypes
from typing import List, Tuple, Optional, Dict, Any
import re
import hashlib
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileSecurityValidator:
    """文件安全验证器"""
    
    # 允许的文件扩展名（白名单）
    ALLOWED_EXTENSIONS = {
        '.txt': 'text/plain',
        '.pdf': 'application/pdf', 
        '.csv': 'text/csv',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel'
    }
    
    # 危险的文件扩展名（黑名单）
    DANGEROUS_EXTENSIONS = {
        '.php', '.php3', '.php4', '.php5', '.phtml', '.phps',
        '.asp', '.aspx', '.jsp', '.jspx',
        '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
        '.sh', '.bash', '.zsh', '.csh',
        '.js', '.vbs', '.ps1', '.psm1',
        '.jar', '.war', '.ear',
        '.py', '.pyc', '.pyo', '.pyw',
        '.pl', '.pm', '.cgi',
        '.rb', '.rbw',
        '.go', '.rs',
        '.c', '.cpp', '.cc', '.cxx', '.h', '.hpp',
        '.java', '.class',
        '.sql', '.db', '.sqlite', '.sqlite3',
        '.htaccess', '.htpasswd',
        '.config', '.conf', '.ini',
        '.xml', '.xsl', '.xslt'
    }
    
    # 最大文件大小 (50MB)
    MAX_FILE_SIZE = 50 * 1024 * 1024
    
    # 文件名中的危险字符
    DANGEROUS_FILENAME_CHARS = r'[<>:"/\\|?*\x00-\x1f]'
    
    def __init__(self):
        """初始化验证器"""
        self.magic_mime = None
        try:
            # 尝试初始化python-magic（如果可用）
            import magic
            self.magic_mime = magic.Magic(mime=True)
        except ImportError:
            logger.warning("python-magic未安装，将使用基础的MIME类型检测")
        except Exception as e:
            logger.warning(f"无法初始化python-magic: {e}")
    
    def validate_filename(self, filename: str) -> Tuple[bool, str]:
        """
        验证文件名安全性
        
        Args:
            filename: 文件名
            
        Returns:
            (is_valid, error_message)
        """
        if not filename:
            return False, "文件名不能为空"
        
        # 检查文件名长度
        if len(filename) > 255:
            return False, "文件名过长（超过255个字符）"
        
        # 检查危险字符
        if re.search(self.DANGEROUS_FILENAME_CHARS, filename):
            return False, "文件名包含非法字符"
        
        # 检查是否以点开头（隐藏文件）
        if filename.startswith('.'):
            return False, "不允许上传隐藏文件"
        
        # 检查双扩展名和多重扩展名
        parts = filename.lower().split('.')
        if len(parts) > 2:  # 超过一个扩展名
            # 检查所有扩展名部分
            for i in range(1, len(parts)):
                ext = '.' + parts[i]
                if ext in self.DANGEROUS_EXTENSIONS:
                    return False, f"检测到危险的扩展名: {ext}"
        
        # 获取最终扩展名
        final_extension = self._get_file_extension(filename)
        if not final_extension:
            return False, "文件必须有扩展名"
        
        # 检查是否在允许列表中
        if final_extension not in self.ALLOWED_EXTENSIONS:
            return False, f"不支持的文件类型: {final_extension}"
        
        return True, ""
    
    def validate_file_content(self, file_content: bytes, filename: str) -> Tuple[bool, str]:
        """
        验证文件内容
        
        Args:
            file_content: 文件内容（字节）
            filename: 文件名
            
        Returns:
            (is_valid, error_message)
        """
        if not file_content:
            return False, "文件内容为空"
        
        # 检查文件大小
        if len(file_content) > self.MAX_FILE_SIZE:
            return False, f"文件过大（超过{self.MAX_FILE_SIZE // (1024*1024)}MB）"
        
        # 获取预期的MIME类型
        expected_extension = self._get_file_extension(filename)
        expected_mime = self.ALLOWED_EXTENSIONS.get(expected_extension)
        
        if not expected_mime:
            return False, f"不支持的文件扩展名: {expected_extension}"
        
        # 检测实际的MIME类型
        detected_mime = self._detect_mime_type(file_content, filename)
        
        # 验证MIME类型匹配
        if not self._is_mime_type_valid(detected_mime, expected_mime):
            return False, f"文件内容与扩展名不匹配。检测到: {detected_mime}, 期望: {expected_mime}"
        
        # 检查文件头部是否包含恶意内容
        if self._contains_malicious_content(file_content):
            return False, "文件包含可疑的恶意内容"
        
        return True, ""
    
    def validate_uploaded_file(self, uploaded_file) -> Tuple[bool, str, Dict[str, Any]]:
        """
        验证上传的文件（Streamlit UploadedFile对象）
        
        Args:
            uploaded_file: Streamlit UploadedFile对象
            
        Returns:
            (is_valid, error_message, file_info)
        """
        try:
            # 验证文件名
            is_valid, error = self.validate_filename(uploaded_file.name)
            if not is_valid:
                return False, error, {}
            
            # 读取文件内容
            file_content = uploaded_file.getbuffer()
            
            # 验证文件内容
            is_valid, error = self.validate_file_content(file_content, uploaded_file.name)
            if not is_valid:
                return False, error, {}
            
            # 生成文件信息
            file_info = {
                'original_name': uploaded_file.name,
                'size': len(file_content),
                'extension': self._get_file_extension(uploaded_file.name),
                'mime_type': self._detect_mime_type(file_content, uploaded_file.name),
                'hash': hashlib.sha256(file_content).hexdigest()[:16]  # 前16位作为标识
            }
            
            return True, "", file_info
            
        except Exception as e:
            logger.error(f"文件验证过程中发生错误: {e}")
            return False, f"文件验证失败: {str(e)}", {}
    
    def _get_file_extension(self, filename: str) -> str:
        """获取文件扩展名（小写）"""
        return os.path.splitext(filename.lower())[1]
    
    def _detect_mime_type(self, file_content: bytes, filename: str) -> str:
        """检测文件的MIME类型"""
        try:
            if self.magic_mime:
                # 使用python-magic检测
                return self.magic_mime.from_buffer(file_content)
            else:
                # 使用标准库的mimetypes
                mime_type, _ = mimetypes.guess_type(filename)
                return mime_type or 'application/octet-stream'
        except Exception as e:
            logger.warning(f"MIME类型检测失败: {e}")
            return 'application/octet-stream'
    
    def _is_mime_type_valid(self, detected_mime: str, expected_mime: str) -> bool:
        """验证MIME类型是否匹配"""
        if not detected_mime or not expected_mime:
            return False
        
        # 精确匹配
        if detected_mime == expected_mime:
            return True
        
        # 特殊情况处理
        mime_mappings = {
            'text/plain': ['text/plain', 'text/x-python', 'text/x-script.python'],
            'application/pdf': ['application/pdf'],
            'text/csv': ['text/csv', 'text/plain', 'application/csv'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/zip'  # xlsx文件实际上是zip格式
            ],
            'application/vnd.ms-excel': [
                'application/vnd.ms-excel',
                'application/msexcel',
                'application/x-msexcel'
            ]
        }
        
        allowed_mimes = mime_mappings.get(expected_mime, [expected_mime])
        return detected_mime in allowed_mimes

    def _contains_malicious_content(self, file_content: bytes) -> bool:
        """检查文件是否包含恶意内容"""
        try:
            # 检查文件头部的前1024字节
            header = file_content[:1024].lower()

            # 检查常见的恶意脚本标识
            malicious_patterns = [
                b'<?php',
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'onload=',
                b'onerror=',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec(',
                b'passthru(',
                b'base64_decode(',
                b'<%',  # ASP标签
                b'<jsp:',  # JSP标签
            ]

            for pattern in malicious_patterns:
                if pattern in header:
                    logger.warning(f"检测到可疑内容: {pattern}")
                    return True

            return False

        except Exception as e:
            logger.error(f"恶意内容检测失败: {e}")
            return True  # 出错时保守处理，认为有风险

    def sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除危险字符

        Args:
            filename: 原始文件名

        Returns:
            清理后的安全文件名
        """
        # 移除危险字符
        safe_filename = re.sub(self.DANGEROUS_FILENAME_CHARS, '_', filename)

        # 确保不以点开头
        if safe_filename.startswith('.'):
            safe_filename = 'file_' + safe_filename[1:]

        # 限制长度
        if len(safe_filename) > 200:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:200-len(ext)] + ext

        return safe_filename


# 全局验证器实例
file_validator = FileSecurityValidator()


def validate_file_upload(uploaded_file) -> Tuple[bool, str, Dict[str, Any]]:
    """
    验证文件上传的便捷函数

    Args:
        uploaded_file: Streamlit UploadedFile对象

    Returns:
        (is_valid, error_message, file_info)
    """
    return file_validator.validate_uploaded_file(uploaded_file)


def is_safe_filename(filename: str) -> bool:
    """
    检查文件名是否安全的便捷函数

    Args:
        filename: 文件名

    Returns:
        是否安全
    """
    is_valid, _ = file_validator.validate_filename(filename)
    return is_valid


def get_safe_filename(filename: str) -> str:
    """
    获取安全文件名的便捷函数

    Args:
        filename: 原始文件名

    Returns:
        安全的文件名
    """
    return file_validator.sanitize_filename(filename)
