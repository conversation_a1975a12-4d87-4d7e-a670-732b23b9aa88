"""
ファイルアップロードセキュリティ検証モジュール
二重拡張子バイパス攻撃やその他のファイルアップロードセキュリティ脆弱性を防止
"""

import os
import mimetypes
from typing import List, Tuple, Optional, Dict, Any
import re
import hashlib
import logging

# ログ設定
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FileSecurityValidator:
    """ファイルセキュリティ検証器"""

    # 許可されたファイル拡張子（ホワイトリスト）
    ALLOWED_EXTENSIONS = {
        '.txt': 'text/plain',
        '.pdf': 'application/pdf', 
        '.csv': 'text/csv',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel'
    }
    
    # 危険なファイル拡張子（ブラックリスト）
    DANGEROUS_EXTENSIONS = {
        '.php', '.php3', '.php4', '.php5', '.phtml', '.phps',
        '.asp', '.aspx', '.jsp', '.jspx',
        '.exe', '.bat', '.cmd', '.com', '.scr', '.pif',
        '.sh', '.bash', '.zsh', '.csh',
        '.js', '.vbs', '.ps1', '.psm1',
        '.jar', '.war', '.ear',
        '.py', '.pyc', '.pyo', '.pyw',
        '.pl', '.pm', '.cgi',
        '.rb', '.rbw',
        '.go', '.rs',
        '.c', '.cpp', '.cc', '.cxx', '.h', '.hpp',
        '.java', '.class',
        '.sql', '.db', '.sqlite', '.sqlite3',
        '.htaccess', '.htpasswd',
        '.config', '.conf', '.ini',
        '.xml', '.xsl', '.xslt'
    }
    
    # 最大ファイルサイズ (50MB)
    MAX_FILE_SIZE = 50 * 1024 * 1024
    
    # ファイル名内の危険な文字
    DANGEROUS_FILENAME_CHARS = r'[<>:"/\\|?*\x00-\x1f]'
    
    def __init__(self):
        """検証器を初期化"""
        self.magic_mime = None
        try:
            # python-magicの初期化を試行（利用可能な場合）
            import magic
            self.magic_mime = magic.Magic(mime=True)
        except ImportError:
            logger.warning("python-magicがインストールされていません。基本的なMIMEタイプ検出を使用します")
        except Exception as e:
            logger.warning(f"python-magicを初期化できません: {e}")
    
    def validate_filename(self, filename: str) -> Tuple[bool, str]:
        """
        ファイル名のセキュリティを検証

        Args:
            filename: ファイル名

        Returns:
            (is_valid, error_message)
        """
        if not filename:
            return False, "ファイル名は空にできません"
        
        # ファイル名の長さをチェック
        if len(filename) > 255:
            return False, "ファイル名が長すぎます（255文字を超過）"
        
        # 危険な文字をチェック
        if re.search(self.DANGEROUS_FILENAME_CHARS, filename):
            return False, "ファイル名に不正な文字が含まれています"
        
        # ドットで始まるかチェック（隠しファイル）
        if filename.startswith('.'):
            return False, "隠しファイルのアップロードは許可されていません"
        
        # 二重拡張子と多重拡張子をチェック
        parts = filename.lower().split('.')
        if len(parts) > 2:  # 複数の拡張子
            # すべての拡張子部分をチェック
            for i in range(1, len(parts)):
                ext = '.' + parts[i]
                if ext in self.DANGEROUS_EXTENSIONS:
                    return False, f"危険な拡張子が検出されました: {ext}"
        
        # 最終拡張子を取得
        final_extension = self._get_file_extension(filename)
        if not final_extension:
            return False, "ファイルには拡張子が必要です"
        
        # 許可リストに含まれているかチェック
        if final_extension not in self.ALLOWED_EXTENSIONS:
            return False, f"サポートされていないファイルタイプ: {final_extension}"
        
        return True, ""
    
    def validate_file_content(self, file_content: bytes, filename: str) -> Tuple[bool, str]:
        """
        ファイル内容を検証

        Args:
            file_content: ファイル内容（バイト）
            filename: ファイル名

        Returns:
            (is_valid, error_message)
        """
        if not file_content:
            return False, "ファイル内容が空です"
        
        # ファイルサイズをチェック
        if len(file_content) > self.MAX_FILE_SIZE:
            return False, f"ファイルが大きすぎます（{self.MAX_FILE_SIZE // (1024*1024)}MBを超過）"
        
        # 期待されるMIMEタイプを取得
        expected_extension = self._get_file_extension(filename)
        expected_mime = self.ALLOWED_EXTENSIONS.get(expected_extension)
        
        if not expected_mime:
            return False, f"サポートされていないファイル拡張子: {expected_extension}"
        
        # 実際のMIMEタイプを検出
        detected_mime = self._detect_mime_type(file_content, filename)
        
        # MIMEタイプの一致を検証
        if not self._is_mime_type_valid(detected_mime, expected_mime):
            return False, f"ファイル内容と拡張子が一致しません。検出: {detected_mime}, 期待: {expected_mime}"
        
        # ファイルヘッダーに悪意のある内容が含まれているかチェック
        if self._contains_malicious_content(file_content):
            return False, "ファイルに疑わしい悪意のある内容が含まれています"
        
        return True, ""
    
    def validate_uploaded_file(self, uploaded_file) -> Tuple[bool, str, Dict[str, Any]]:
        """
        アップロードされたファイルを検証（Streamlit UploadedFileオブジェクト）

        Args:
            uploaded_file: Streamlit UploadedFileオブジェクト

        Returns:
            (is_valid, error_message, file_info)
        """
        try:
            # ファイル名を検証
            is_valid, error = self.validate_filename(uploaded_file.name)
            if not is_valid:
                return False, error, {}
            
            # ファイル内容を読み取り
            file_content = uploaded_file.getbuffer()
            
            # ファイル内容を検証
            is_valid, error = self.validate_file_content(file_content, uploaded_file.name)
            if not is_valid:
                return False, error, {}
            
            # ファイル情報を生成
            file_info = {
                'original_name': uploaded_file.name,
                'size': len(file_content),
                'extension': self._get_file_extension(uploaded_file.name),
                'mime_type': self._detect_mime_type(file_content, uploaded_file.name),
                'hash': hashlib.sha256(file_content).hexdigest()[:16]  # 最初の16文字を識別子として使用
            }
            
            return True, "", file_info
            
        except Exception as e:
            logger.error(f"ファイル検証プロセス中にエラーが発生しました: {e}")
            return False, f"ファイル検証に失敗しました: {str(e)}", {}
    
    def _get_file_extension(self, filename: str) -> str:
        """ファイル拡張子を取得（小文字）"""
        return os.path.splitext(filename.lower())[1]
    
    def _detect_mime_type(self, file_content: bytes, filename: str) -> str:
        """ファイルのMIMEタイプを検出"""
        try:
            if self.magic_mime:
                # python-magicを使用して検出
                return self.magic_mime.from_buffer(file_content)
            else:
                # 標準ライブラリのmimetypesを使用
                mime_type, _ = mimetypes.guess_type(filename)
                return mime_type or 'application/octet-stream'
        except Exception as e:
            logger.warning(f"MIMEタイプ検出に失敗しました: {e}")
            return 'application/octet-stream'
    
    def _is_mime_type_valid(self, detected_mime: str, expected_mime: str) -> bool:
        """MIMEタイプが一致するかを検証"""
        if not detected_mime or not expected_mime:
            return False
        
        # 完全一致
        if detected_mime == expected_mime:
            return True
        
        # 特殊ケースの処理
        mime_mappings = {
            'text/plain': ['text/plain', 'text/x-python', 'text/x-script.python'],
            'application/pdf': ['application/pdf'],
            'text/csv': ['text/csv', 'text/plain', 'application/csv'],
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/zip'  # xlsxファイルは実際にはzip形式
            ],
            'application/vnd.ms-excel': [
                'application/vnd.ms-excel',
                'application/msexcel',
                'application/x-msexcel'
            ]
        }
        
        allowed_mimes = mime_mappings.get(expected_mime, [expected_mime])
        return detected_mime in allowed_mimes

    def _contains_malicious_content(self, file_content: bytes) -> bool:
        """ファイルに悪意のある内容が含まれているかチェック"""
        try:
            # ファイルヘッダーの最初の1024バイトをチェック
            header = file_content[:1024].lower()

            # 一般的な悪意のあるスクリプト識別子をチェック
            malicious_patterns = [
                b'<?php',
                b'<script',
                b'javascript:',
                b'vbscript:',
                b'onload=',
                b'onerror=',
                b'eval(',
                b'exec(',
                b'system(',
                b'shell_exec(',
                b'passthru(',
                b'base64_decode(',
                b'<%',  # ASPタグ
                b'<jsp:',  # JSPタグ
            ]

            for pattern in malicious_patterns:
                if pattern in header:
                    logger.warning(f"疑わしい内容を検出しました: {pattern}")
                    return True

            return False

        except Exception as e:
            logger.error(f"悪意のある内容の検出に失敗しました: {e}")
            return True  # エラー時は保守的に処理し、リスクがあると判断

    def sanitize_filename(self, filename: str) -> str:
        """
        ファイル名をクリーンアップし、危険な文字を削除

        Args:
            filename: 元のファイル名

        Returns:
            クリーンアップされた安全なファイル名
        """
        # 危険な文字を削除
        safe_filename = re.sub(self.DANGEROUS_FILENAME_CHARS, '_', filename)

        # ドットで始まらないことを確認
        if safe_filename.startswith('.'):
            safe_filename = 'file_' + safe_filename[1:]

        # 長さを制限
        if len(safe_filename) > 200:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:200-len(ext)] + ext

        return safe_filename


# グローバル検証器インスタンス
file_validator = FileSecurityValidator()


def validate_file_upload(uploaded_file) -> Tuple[bool, str, Dict[str, Any]]:
    """
    ファイルアップロード検証の便利関数

    Args:
        uploaded_file: Streamlit UploadedFileオブジェクト

    Returns:
        (is_valid, error_message, file_info)
    """
    return file_validator.validate_uploaded_file(uploaded_file)


def is_safe_filename(filename: str) -> bool:
    """
    ファイル名が安全かチェックする便利関数

    Args:
        filename: ファイル名

    Returns:
        安全かどうか
    """
    is_valid, _ = file_validator.validate_filename(filename)
    return is_valid


def get_safe_filename(filename: str) -> str:
    """
    安全なファイル名を取得する便利関数

    Args:
        filename: 元のファイル名

    Returns:
        安全なファイル名
    """
    return file_validator.sanitize_filename(filename)
