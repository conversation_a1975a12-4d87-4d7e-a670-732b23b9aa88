import requests
from bs4 import BeautifulSoup
import os
from urllib.parse import urljoin, urlparse, urldefrag
import time
import chardet
from datetime import datetime
import re

def is_same_domain(base_url, url):
    """同じドメインかどうかをチェックする関数"""
    base_domain = urlparse(base_url).netloc
    url_domain = urlparse(url).netloc
    return base_domain == url_domain

def normalize_url(url):
    """URLを正規化する関数"""
    # 二重プロトコルのチェックと修正
    if url.startswith('https://http://'):
        url = url.replace('https://', '', 1)
    elif url.startswith('http://https://'):
        url = url.replace('http://', '', 1)
    
    parsed_url = urlparse(url)
    normalized_url = parsed_url.scheme + "://" + parsed_url.netloc + parsed_url.path
    if parsed_url.query:
        normalized_url += "?" + parsed_url.query
    return normalized_url

def get_all_links(soup, base_url):
    """ページ内の全リンクを取得する関数"""
    links = set()
    for a_tag in soup.find_all('a', href=True):
        url = urljoin(base_url, a_tag['href'])
        normalized_url = normalize_url(url)  # URLを正規化
        normalized_url = normalized_url.rstrip('/')  # Remove trailing slashes
        
        # 同じドメインのURLかつ画像/PDFファイルでないもののみを取得
        if is_same_domain(base_url, normalized_url) and not any(
            normalized_url.lower().endswith(ext) 
            for ext in ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', 
                       '.ico', '.svg', '.tiff', '.heic', '.pdf']
        ):
            links.add(normalized_url)
    return links

def extract_page_content(soup, url):
    """ページのコンテンツを抽出する関数"""
    content_parts = []
    
    # メタ情報の取得と追加
    title = soup.title.string if soup.title else "タイトルなし"
    content_parts.append(f"URL: {url}")
    content_parts.append(f"タイトル: {title}")
    content_parts.append("=" * 80)
    
    # 不要な要素を削除
    for tag in soup.find_all(['script', 'style', 'noscript', 'iframe', 'header', 'footer', 'nav']):
        tag.decompose()
    
    # メインコンテンツの抽出
    main_content = []
    
    # 主要なコンテナ要素を探す
    main_containers = soup.find_all(['main', 'article', 'section', 'div'], 
        class_=lambda x: x and any(keyword in str(x).lower() 
            for keyword in ['main', 'content', 'article', 'section', 'container']))
    
    if not main_containers:
        # コンテナが見つからない場合は、bodyから直接抽出
        main_containers = [soup.body] if soup.body else [soup]
    
    for container in main_containers:
        # テキストを含む全ての要素を取得
        for element in container.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'p', 'ul', 'ol', 'dl', 'div']):
            # クラス名でフィルタリング（明らかなナビゲーションやフッターを除外）
            if element.get('class') and any(keyword in ' '.join(element.get('class')).lower() 
                for keyword in ['nav', 'menu', 'footer', 'header', 'sidebar']):
                continue
            
            text = element.get_text().strip()
            if not text:
                continue
            
            # 見出しの処理
            if element.name in ['h1', 'h2', 'h3', 'h4', 'h5', 'h6']:
                if text not in main_content:  # 重複チェック
                    main_content.append("")  # セクション間の区切り
                    main_content.append(f"【{text}】")
            
            # 段落とdivの処理
            elif element.name in ['p', 'div']:
                if text not in main_content:  # 重複チェック
                    # 最小文字数チェック（短すぎる文は除外）
                    if len(text) > 10:
                        main_content.append(text)
            
            # リストの処理
            elif element.name in ['ul', 'ol']:
                list_items = []
                for li in element.find_all('li'):
                    li_text = li.get_text().strip()
                    if li_text and len(li_text) > 5:  # 短すぎるリスト項目は除外
                        list_items.append(f"・{li_text}")
                if list_items:
                    main_content.append("\n".join(list_items))
            
            # 定義リストの処理
            elif element.name == 'dl':
                for dt, dd in zip(element.find_all('dt'), element.find_all('dd')):
                    dt_text = dt.get_text().strip()
                    dd_text = dd.get_text().strip()
                    if dt_text and dd_text:
                        main_content.append(f"{dt_text}: {dd_text}")
    
    # メインコンテンツの追加（空行の重複を除去）
    prev_line = None
    for line in main_content:
        if line != prev_line:  # 連続する同じ行を除去
            content_parts.append(line)
            prev_line = line
    
    # 最終的なテキストの作成（過剰な空行を整理）
    text = "\n".join(content_parts)
    while "\n\n\n" in text:
        text = text.replace("\n\n\n", "\n\n")
    
    return text


def crawl_and_save_as_text(url, filename, max_pages=100, progress_callback=None, ignored_extensions=None):
    """
    URLからテキストを取得してファイルに保存する
    
    Args:
        url: クロールするURL
        filename: 保存するファイル名
        max_pages: クロールする最大ページ数
        progress_callback: 進捗を報告するコールバック関数
        ignored_extensions: 無視する拡張子のリスト
    """
    # URLの正規化
    if url.startswith('http://'):
        url = url  # そのまま使用
    elif url.startswith('https://'):
        url = url  # そのまま使用
    else:
        # プロトコルがない場合はhttpを試し、失敗したらhttpsを試す
        try:
            test_url = f'http://{url}'
            response = requests.head(test_url, timeout=5)
            url = test_url
        except:
            url = f'https://{url}'
        
    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        
        visited_urls = set()
        all_content = []
        urls_to_visit = {normalize_url(url)}  # URLを正規化して初期化
        
        if progress_callback:
            progress_callback(0, f"クローリングを開始します: {url}")
        
        # URLをチェックする際に拡張子を確認
        def should_ignore_url(url):
            if ignored_extensions:
                return any(url.lower().endswith(ext) for ext in ignored_extensions)
            return False
        
        while urls_to_visit and len(visited_urls) < max_pages:
            current_url = urls_to_visit.pop()
            
            # 既に訪問済みまたは画像ファイルの場合はスキップ
            if current_url in visited_urls or should_ignore_url(current_url):
                continue
            
            try:
                if progress_callback:
                    progress = len(visited_urls) / max_pages
                    progress_callback(progress, f"ページをクロール中: {current_url}")
                
                # ページの取得と処理
                response = requests.get(current_url, headers=headers)
                response.raise_for_status()

                # Detect encoding if not UTF-8
                content_type = response.headers.get("content-type", "")
                m = re.search(r"charset=([^\s;]+)", content_type, re.I)
                if m:
                    response.encoding = m.group(1).strip(' "\'')  # header に charset があれば即採用
                else:
                    # Detect encoding if not UTF-8
                    if not response.encoding or response.encoding.lower() != 'utf-8':
                        detected = chardet.detect(response.content)
                        response.encoding = detected.get('encoding', 'utf-8')

                soup = BeautifulSoup(response.text, 'html.parser')

                for tag in soup(['script', 'style', 'meta', 'link', 'noscript']):
                    tag.decompose()
                
                content = extract_page_content(soup, current_url)
                all_content.append(content)
                
                new_links = get_all_links(soup, url)
                
                # 既に訪問したURLを除外
                new_links_to_visit = new_links - visited_urls
                urls_to_visit.update(new_links_to_visit)
                visited_urls.add(current_url)
                
                time.sleep(1)
                
            except Exception as e:
                if progress_callback:
                    progress_callback(None, f"エラー: {current_url} - {str(e)}")
                continue
        
        if progress_callback:
            progress_callback(0.9, "ファイルを保存中...")
        
        # ファイルの保存
        output_filename = os.path.join(os.path.dirname(__file__), "files", filename)
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write("\n\n".join(all_content))
            f.write(f"\n\n合計クロールページ数: {len(visited_urls)}\n")
            f.write(f"クロール開始URL: {url}\n")
            f.write(f"クロール完了時刻: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        
        if progress_callback:
            progress_callback(1.0, f"クローリングが完了しました: {url}")
        
        return True, output_filename
        
    except Exception as e:
        if progress_callback:
            progress_callback(None, f"エラーが発生しました ({url}): {str(e)}")
        print(f"Error in crawl_and_save_as_text for {url}: {str(e)}")
        return False, None