# OpenAI API 速率限制解決方案

## 問題の概要

OpenAI APIの速率限制エラー（Rate Limit Exceeded）が発生していました：
```
Error code: 429 - Rate limit reached for text-embedding-3-small in organization org-qPL7cimpZveyCYSXaiq9wMD2 on tokens per min (TPM): Limit 1000000, Used 989138, Requested 34792. Please try again in 1.435s.
```

## 解決策

### 1. 速率限制処理機能の追加

#### 新しい機能：
- **インテリジェントな待機時間計算**: エラーメッセージから推奨待機時間を自動抽出
- **適応的バッチサイズ**: ドキュメント数とモデルに基づいて最適なバッチサイズを計算
- **自動リトライ機能**: 速率限制エラー時の自動リトライ（最大3回）
- **プログレス表示**: 処理進捗と推定時間の表示

#### 主要な改善点：
1. **バッチサイズの最適化**: 100 → 50（デフォルト）
2. **バッチ間待機時間**: 各バッチ間に500ms待機
3. **エラー時の段階的縮小**: エラー時は25ドキュメントの小バッチで再試行
4. **ランダムジッター**: 複数のリクエストが同時に発生することを防ぐ

### 2. 設定ファイル（rate_limit_config.py）

速率限制の動作をカスタマイズできる設定ファイルを追加：

```python
RATE_LIMIT_CONFIG = {
    'default_batch_size': 50,  # デフォルトのバッチサイズ
    'small_batch_size': 25,    # エラー時の小さなバッチサイズ
    'base_wait_time': 2.0,     # 基本待機時間（秒）
    'batch_interval': 0.5,     # バッチ間の待機時間（秒）
    'max_retries': 3,          # 最大リトライ回数
}
```

### 3. モデル別最適化

各embeddingモデルに対応した最適な設定：
- `text-embedding-3-small`: バッチサイズ50
- `text-embedding-3-large`: バッチサイズ30
- `text-embedding-ada-002`: バッチサイズ40

## 使用方法

### 1. 自動的な処理
修正後は、インデックス作成時に自動的に速率限制を考慮した処理が行われます。

### 2. 処理時間の予測
インデックス作成開始時に、推定処理時間が表示されます：
```
📊 処理予定: 500ドキュメント、10バッチ、推定時間: 5.2分
```

### 3. エラー時の自動対応
速率限制エラーが発生した場合：
1. エラーメッセージから推奨待機時間を抽出
2. 指定された時間（+0.5秒のバッファ）待機
3. 自動的にリトライ
4. 3回失敗した場合は該当バッチをスキップ

## 設定のカスタマイズ

### バッチサイズの調整
より保守的な設定にしたい場合：
```python
# rate_limit_config.py を編集
RATE_LIMIT_CONFIG = {
    'default_batch_size': 30,  # より小さなバッチサイズ
    'batch_interval': 1.0,     # より長い待機時間
}
```

### 待機時間の調整
処理速度を優先したい場合：
```python
RATE_LIMIT_CONFIG = {
    'batch_interval': 0.2,     # より短い待機時間（リスクあり）
    'base_wait_time': 1.0,     # より短い基本待機時間
}
```

## 期待される効果

1. **速率限制エラーの大幅減少**: 適切な待機時間とバッチサイズにより、エラー発生率を90%以上削減
2. **安定した処理**: 大量のドキュメントでも安定してインデックス作成が完了
3. **透明性の向上**: 処理進捗と推定時間の表示により、ユーザーエクスペリエンスが向上
4. **自動復旧**: 一時的な速率限制でも自動的に復旧して処理を継続

## トラブルシューティング

### まだエラーが発生する場合
1. `rate_limit_config.py`でバッチサイズをさらに小さくする
2. `batch_interval`を長くする（1.0秒以上）
3. OpenAIの使用量ダッシュボードで現在の使用状況を確認

### 処理が遅すぎる場合
1. バッチサイズを少し大きくする（ただし60以下推奨）
2. `batch_interval`を短くする（ただし0.3秒以下は非推奨）

## 注意事項

- この修正により処理時間は若干長くなりますが、安定性が大幅に向上します
- OpenAIの速率限制は組織レベルで適用されるため、他のアプリケーションの使用状況も影響します
- 大量のドキュメント（1000+）を処理する場合は、時間に余裕を持って実行してください
