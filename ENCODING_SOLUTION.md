# 文字エンコーディング問題の解決方案

## 問題の概要

ファイル読み込み時に以下のエラーが発生していました：

```
'gbk' codec can't decode byte 0x80 in position 70: illegal multibyte sequence
'gbk' codec can't decode byte 0xab in position 72: illegal multibyte sequence
```

これは、文字エンコーディングの自動検出が正しく動作せず、実際にはUTF-8エンコードされたファイルをGBKエンコーディングで読み込もうとしたために発生しました。

## 解決策

### 1. 強化された CustomTextLoader クラス

#### 主な改善点：
- **多段階エンコーディング検出**: chardetの結果に加えて、よくある日本語エンコーディングを順番に試行
- **GBK誤検出対策**: GBKが検出された場合、UTF-8での読み込みを優先的に試行
- **フォールバック機能**: すべてのエンコーディングで失敗した場合、エラーを無視してUTF-8で読み込み
- **詳細なメタデータ**: 使用されたエンコーディングと検出されたエンコーディングを記録

#### 試行するエンコーディングの順序：
1. chardetで検出されたエンコーディング
2. UTF-8
3. UTF-8 with BOM
4. Shift_JIS
5. EUC-JP
6. ISO-2022-JP
7. CP932
8. Latin1（最後の手段）

### 2. エラーハンドリングの改善

#### 個別ファイルエラーの処理：
- 単一ファイルの読み込み失敗が全体の処理を停止しないように改善
- エラーファイルをスキップして処理を継続
- 警告メッセージで問題のあるファイルを通知

#### ドキュメント数チェック：
- 読み込み可能なドキュメントがない場合の適切な処理
- 成功したドキュメント数の表示

### 3. BM25インデックス作成の安定化

BM25インデックス作成時のエラーハンドリングを追加し、失敗してもベクトルインデックスは正常に動作するように改善。

## 使用方法

### 1. 自動的な処理
修正後は、ファイルアップロード時に自動的に最適なエンコーディングが検出・使用されます。

### 2. エンコーディングテスト
問題のあるファイルがある場合、以下のコマンドでテストできます：

```bash
python test_encoding.py
```

### 3. 手動でのファイル確認
特定のファイルのエンコーディングを確認したい場合：

```python
from test_encoding import test_file_encoding
test_file_encoding("files/your_file.txt")
```

## 期待される効果

### 1. エンコーディングエラーの解決
- GBK誤検出問題の解決
- 様々なエンコーディングのファイルに対応
- 文字化けの大幅な減少

### 2. 処理の安定性向上
- 単一ファイルの問題が全体に影響しない
- より多くのファイル形式に対応
- エラー時の適切なフォールバック

### 3. ユーザーエクスペリエンスの向上
- 明確なエラーメッセージ
- 処理状況の可視化
- 問題ファイルの特定が容易

## トラブルシューティング

### まだエンコーディングエラーが発生する場合

1. **ファイルの確認**:
   ```bash
   file -i your_file.txt  # Linuxの場合
   ```

2. **手動でのエンコーディング指定**:
   ファイルを適切なエンコーディングで保存し直す

3. **テストスクリプトの実行**:
   ```bash
   python test_encoding.py
   ```

### 文字化けが発生する場合

1. **元ファイルのエンコーディング確認**
2. **UTF-8での保存し直し**（推奨）
3. **ファイル名の英数字化**

### 処理が完全に失敗する場合

1. **ファイル権限の確認**
2. **ファイルサイズの確認**（大きすぎないか）
3. **ファイル形式の確認**（対応形式か）

## 推奨事項

### 1. ファイル準備時
- **UTF-8エンコーディング**での保存を推奨
- **BOM付きUTF-8**は避ける
- **ファイル名は英数字**を使用

### 2. 大量ファイル処理時
- **バッチ処理**で少しずつアップロード
- **エラーログの確認**
- **問題ファイルの事前特定**

### 3. 定期的なメンテナンス
- **エンコーディング統一**
- **ファイル形式の標準化**
- **不要ファイルの削除**

## 技術的詳細

### エンコーディング検出アルゴリズム
1. chardetライブラリによる自動検出
2. 信頼度チェック（70%未満は再検証）
3. 日本語エンコーディングの順次試行
4. GBK誤検出の特別処理
5. エラー無視モードでのフォールバック

### メタデータの活用
各ドキュメントには以下の情報が付与されます：
- `encoding`: 実際に使用されたエンコーディング
- `detected_encoding`: chardetで検出されたエンコーディング
- `warning`: 警告メッセージ（該当する場合）

これにより、後から問題のあるファイルを特定・修正することが可能です。
