import os
import time
import streamlit as st
from datetime import datetime
import sys
import subprocess
import glob
import requests
import json
import openai
import pandas as pd
from dotenv import load_dotenv
from streamlit_chat import message
from langchain.document_loaders import PyPDFLoader, CSVLoader, TextLoader
from langchain.embeddings.openai import OpenAIEmbeddings
from langchain.vectorstores import FAISS
from langchain_openai import ChatOpenAI
from langchain.text_splitter import RecursiveCharacterTextSplitter
from crawler import crawl_and_save_as_text
from langchain.schema import Document
from langchain_community.retrievers import BM25Retriever
from langchain.prompts import PromptTemplate
import pickle
import chardet
import sqlite3
import hashlib
from google import genai
import random
from file_security import is_safe_filename, get_safe_filename
import json

# 速率限制設定をインポート
try:
    from rate_limit_config import RATE_LIMIT_CONFIG, calculate_optimal_batch_size, estimate_processing_time
except ImportError:
    # 設定ファイルがない場合のデフォルト設定
    RATE_LIMIT_CONFIG = {
        'default_batch_size': 50,
        'small_batch_size': 25,
        'base_wait_time': 2.0,
        'batch_interval': 0.5,
        'error_wait_buffer': 0.5,
        'max_retries': 3,
    }

    def calculate_optimal_batch_size(document_count, model_name='text-embedding-3-small'):
        return min(50, max(10, document_count // 10))

    def estimate_processing_time(document_count, model_name='text-embedding-3-small'):
        return {'estimated_minutes': document_count / 100, 'total_batches': document_count // 50, 'batch_size': 50}

# Load environment variables, overriding any existing ones, to ensure fresh values are used.
load_dotenv(override=True)

def handle_rate_limit_error(error_message, batch_number):
    """
    速率限制エラーを処理し、適切な待機時間を返す
    """
    try:
        # エラーメッセージから待機時間を抽出
        if "Please try again in" in error_message:
            # "Please try again in 1.435s" のような形式から秒数を抽出
            import re
            match = re.search(r'Please try again in (\d+\.?\d*)s', error_message)
            if match:
                wait_time = float(match.group(1))
                # 少し余裕を持たせる（+0.5秒）
                return wait_time + 0.5

        # デフォルトの待機時間（バッチ番号に基づいて調整）
        base_wait = RATE_LIMIT_CONFIG['base_wait_time']
        jitter = random.uniform(0.1, 0.5)  # ランダムなジッターを追加
        return base_wait + (batch_number * 0.1) + jitter

    except Exception:
        # エラー解析に失敗した場合のデフォルト待機時間
        return 3.0 + random.uniform(0.1, 0.5)

def process_documents_with_rate_limiting(documents, embeddings, progress_placeholder=None):
    """
    速率限制を考慮してドキュメントを処理する
    """
    # 最適なバッチサイズを計算
    embedding_model = os.getenv('EMBEDDING_MODEL', 'text-embedding-3-small')
    batch_size = calculate_optimal_batch_size(len(documents), embedding_model)

    # 処理時間の概算を表示
    time_estimate = estimate_processing_time(len(documents), embedding_model)
    st.info(f"📊 処理予定: {len(documents)}ドキュメント、{time_estimate['total_batches']}バッチ、推定時間: {time_estimate['estimated_minutes']:.1f}分")

    vector_store = None
    total_batches = (len(documents) + batch_size - 1) // batch_size

    for i in range(0, len(documents), batch_size):
        batch_docs = documents[i:i + batch_size]
        batch_number = i // batch_size + 1

        # プログレスバーの更新
        if progress_placeholder:
            progress = batch_number / total_batches
            progress_placeholder.progress(
                progress,
                text=f"インデックス作成中... ({batch_number}/{total_batches})"
            )

        max_retries = RATE_LIMIT_CONFIG['max_retries']
        retry_count = 0

        while retry_count < max_retries:
            try:
                if vector_store is None:
                    vector_store = FAISS.from_documents(batch_docs, embeddings)
                else:
                    vector_store.add_documents(batch_docs)

                # 成功した場合、次のバッチへ
                break

            except Exception as e:
                error_str = str(e)

                if "rate_limit_exceeded" in error_str or "Rate limit" in error_str:
                    retry_count += 1
                    wait_time = handle_rate_limit_error(error_str, batch_number)

                    if retry_count < max_retries:
                        st.warning(f"バッチ {batch_number}: 速率限制に達しました。{wait_time:.1f}秒待機してリトライします... ({retry_count}/{max_retries})")
                        time.sleep(wait_time)
                    else:
                        st.error(f"バッチ {batch_number}: 最大リトライ回数に達しました。スキップします。")
                        break
                else:
                    # 速率限制以外のエラーの場合
                    st.warning(f"バッチ {batch_number}でエラーが発生しました: {e}")
                    # より小さなバッチサイズで再試行
                    smaller_batch_size = RATE_LIMIT_CONFIG['small_batch_size']
                    for j in range(0, len(batch_docs), smaller_batch_size):
                        smaller_batch = batch_docs[j:j + smaller_batch_size]
                        try:
                            if vector_store is None:
                                vector_store = FAISS.from_documents(smaller_batch, embeddings)
                            else:
                                vector_store.add_documents(smaller_batch)
                        except Exception as e2:
                            st.error(f"小バッチ処理に失敗しました: {e2}")
                    break

        # バッチ間の待機時間（速率限制を予防）
        if batch_number < total_batches:  # 最後のバッチでない場合
            time.sleep(RATE_LIMIT_CONFIG['batch_interval'])

    return vector_store

def clean_text_content(text):
    """
    テキスト内容をクリーンアップし、問題のある文字を除去する
    """
    if not text:
        return text

    try:
        # 基本的なクリーニング
        cleaned_text = text

        # 制御文字を除去（改行とタブは保持）
        import re
        cleaned_text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', cleaned_text)

        # 無効なUnicode文字を除去
        cleaned_text = cleaned_text.encode('utf-8', errors='ignore').decode('utf-8')

        # 連続する空白を正規化
        cleaned_text = re.sub(r'\s+', ' ', cleaned_text)

        # 空白のみの行を除去
        lines = cleaned_text.split('\n')
        cleaned_lines = [line.strip() for line in lines if line.strip()]
        cleaned_text = '\n'.join(cleaned_lines)

        return cleaned_text

    except Exception as e:
        # クリーニングに失敗した場合は空文字列を返す
        return ""

def split_documents_with_error_handling(text_splitter, documents, file_path):
    """
    ドキュメントを分割し、問題のあるチャンクをスキップする
    """
    valid_chunks = []
    skipped_chunks = 0

    for doc in documents:
        try:
            # まずテキスト内容をクリーンアップ
            cleaned_content = clean_text_content(doc.page_content)

            if not cleaned_content:
                skipped_chunks += 1
                continue

            # クリーンアップされた内容で新しいドキュメントを作成
            cleaned_doc = Document(
                page_content=cleaned_content,
                metadata={**doc.metadata, "cleaned": True}
            )

            # ドキュメントを分割
            split_docs = text_splitter.split_documents([cleaned_doc])

            # 各チャンクを個別に検証
            for chunk in split_docs:
                try:
                    # チャンクの内容を検証（エンコーディングテスト）
                    test_content = chunk.page_content.encode('utf-8').decode('utf-8')

                    # 最小長チェック（あまりに短いチャンクは除外）
                    if len(test_content.strip()) >= 10:
                        valid_chunks.append(chunk)
                    else:
                        skipped_chunks += 1

                except (UnicodeError, UnicodeDecodeError, UnicodeEncodeError):
                    skipped_chunks += 1
                    continue
                except Exception:
                    skipped_chunks += 1
                    continue

        except Exception as e:
            # ドキュメント全体の処理に失敗した場合
            skipped_chunks += 1
            continue

    if skipped_chunks > 0:
        st.info(f"📝 ファイル '{file_path}': {len(valid_chunks)}個の有効なチャンクを取得、{skipped_chunks}個の問題のあるチャンクをスキップしました")

    return valid_chunks

# Use the same password logic as in file.py
# It will use the ADMIN_PASSWORD from the .env file, or default to "password"
default_password_for_aichat = "password"
STORED_PASSWORD_HASH = hashlib.sha256(os.getenv('ADMIN_PASSWORD', default_password_for_aichat).encode()).hexdigest()

# 持久化された認証失敗記録の管理（file.pyと同じ）
AUTH_FAILURE_FILE = "auth_failures.json"

def load_auth_failures():
    """認証失敗記録を読み込む"""
    try:
        if os.path.exists(AUTH_FAILURE_FILE):
            with open(AUTH_FAILURE_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return data.get('attempts', 0), data.get('last_failed_time', 0)
    except Exception as e:
        print(f"認証失敗記録の読み込みエラー: {e}")
    return 0, 0

def save_auth_failures(attempts, last_failed_time):
    """認証失敗記録を保存する"""
    try:
        data = {
            'attempts': attempts,
            'last_failed_time': last_failed_time,
            'updated_at': time.time()
        }
        with open(AUTH_FAILURE_FILE, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"認証失敗記録の保存エラー: {e}")

def reset_auth_failures():
    """認証失敗記録をリセットする"""
    try:
        if os.path.exists(AUTH_FAILURE_FILE):
            os.remove(AUTH_FAILURE_FILE)
    except Exception as e:
        print(f"認証失敗記録のリセットエラー: {e}")

def check_auth_lockout():
    """認証ロックアウト状態をチェックする"""
    attempts, last_failed_time = load_auth_failures()

    if attempts >= 3:
        time_since_last_failed = time.time() - last_failed_time
        lockout_duration = 300  # 5分間

        if time_since_last_failed >= lockout_duration:
            # ロックアウト期間が過ぎたのでリセット
            reset_auth_failures()
            return False, 0
        else:
            # まだロックアウト中
            remaining_time = lockout_duration - time_since_last_failed
            return True, remaining_time

    return False, 0

def record_auth_failure():
    """認証失敗を記録する"""
    attempts, _ = load_auth_failures()
    attempts += 1
    current_time = time.time()
    save_auth_failures(attempts, current_time)
    return attempts

class CustomTextLoader:
    def __init__(self, file_path):
        self.file_path = file_path

    def detect_encoding(self):
        """
        より堅牢な文字エンコーディング検出
        """
        try:
            with open(self.file_path, 'rb') as f:
                raw_data = f.read()

            # chardetで検出を試行
            result = chardet.detect(raw_data)
            detected_encoding = result['encoding']
            confidence = result['confidence']

            # 信頼度が低い場合や検出に失敗した場合の対処
            if not detected_encoding or confidence < 0.7:
                # よくある日本語エンコーディングを順番に試す
                common_encodings = ['utf-8', 'utf-8-sig', 'shift_jis', 'euc-jp', 'iso-2022-jp']
                for encoding in common_encodings:
                    try:
                        raw_data.decode(encoding)
                        return encoding
                    except UnicodeDecodeError:
                        continue

                # それでも失敗した場合はUTF-8をデフォルトとして使用
                return 'utf-8'

            # GBKが検出された場合、実際にはUTF-8の可能性が高い
            if detected_encoding.lower() in ['gbk', 'gb2312']:
                try:
                    raw_data.decode('utf-8')
                    return 'utf-8'
                except UnicodeDecodeError:
                    pass

            return detected_encoding

        except Exception as e:
            st.warning(f"エンコーディング検出でエラーが発生しました ({self.file_path}): {e}")
            return 'utf-8'  # デフォルトとしてUTF-8を使用

    def load(self):
        """
        複数のエンコーディングを試行してファイルを読み込む
        """
        # まず検出されたエンコーディングを試す
        detected_encoding = self.detect_encoding()

        # 試行するエンコーディングのリスト
        encodings_to_try = [
            detected_encoding,
            'utf-8',
            'utf-8-sig',
            'shift_jis',
            'euc-jp',
            'iso-2022-jp',
            'cp932',
            'latin1'  # 最後の手段として
        ]

        # 重複を除去
        encodings_to_try = list(dict.fromkeys(encodings_to_try))

        for encoding in encodings_to_try:
            if not encoding:
                continue

            try:
                with open(self.file_path, 'r', encoding=encoding, errors='strict') as f:
                    content = f.read()

                # 成功した場合、ファイル名にエンコーディング情報を追加
                metadata = {
                    "source": self.file_path,
                    "encoding": encoding,
                    "detected_encoding": detected_encoding
                }

                return [Document(page_content=content, metadata=metadata)]

            except (UnicodeDecodeError, UnicodeError) as e:
                continue
            except Exception as e:
                st.warning(f"ファイル読み込みエラー ({self.file_path}, {encoding}): {e}")
                continue

        # すべてのエンコーディングで失敗した場合、行ごとに処理を試行
        try:
            valid_lines = []
            skipped_lines = 0

            with open(self.file_path, 'rb') as f:
                for line_num, line_bytes in enumerate(f, 1):
                    # 各行を個別に処理
                    for encoding in ['utf-8', 'shift_jis', 'euc-jp', 'cp932']:
                        try:
                            line_text = line_bytes.decode(encoding)
                            # 制御文字をクリーンアップ
                            line_text = clean_text_content(line_text)
                            if line_text.strip():  # 空でない行のみ追加
                                valid_lines.append(line_text)
                            break
                        except UnicodeDecodeError:
                            continue
                    else:
                        # すべてのエンコーディングで失敗した行はスキップ
                        skipped_lines += 1

            if valid_lines:
                content = '\n'.join(valid_lines)

                if skipped_lines > 0:
                    st.warning(f"ファイル '{self.file_path}': {len(valid_lines)}行を読み込み、{skipped_lines}行の問題のある行をスキップしました。")
                else:
                    st.info(f"ファイル '{self.file_path}': {len(valid_lines)}行を正常に読み込みました。")

                metadata = {
                    "source": self.file_path,
                    "encoding": "mixed (line-by-line processing)",
                    "detected_encoding": detected_encoding,
                    "valid_lines": len(valid_lines),
                    "skipped_lines": skipped_lines,
                    "warning": f"Processed line by line, {skipped_lines} lines skipped" if skipped_lines > 0 else None
                }

                return [Document(page_content=content, metadata=metadata)]
            else:
                raise Exception(f"ファイル '{self.file_path}' から有効な行を読み込めませんでした")

        except Exception as e:
            raise Exception(f"ファイル '{self.file_path}' の読み込みに完全に失敗しました: {e}")

def get_embeddings():
    load_dotenv(override=True)
    embedding_provider = os.getenv('EMBEDDING_PROVIDER', 'openai')
    
    if embedding_provider == 'openai':
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI APIキーが設定されていません")
        embedding_model = os.getenv('EMBEDDING_MODEL', 'text-embedding-3-small')
        return OpenAIEmbeddings(
            model=embedding_model,
            openai_api_key=api_key,
            max_retries=3,  # 最大リトライ回数
            request_timeout=60,  # タイムアウト時間（秒）
        )
    else:  # ollama
        from langchain.embeddings import OllamaEmbeddings
        base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
        model = os.getenv('OLLAMA_EMBED_MODEL', 'llama2')
        return OllamaEmbeddings(
            base_url=base_url,
            model=model
        )

def get_chat_model():
    load_dotenv(override=True)
    active_provider = os.getenv('ACTIVE_PROVIDER', 'openai')
    
    if active_provider == 'openai':
        api_key = os.environ.get("OPENAI_API_KEY")
        if not api_key:
            raise ValueError("OpenAI APIキーが設定されていません")
        chat_model = os.getenv('CHAT_MODEL', 'gpt-4o')
        return ChatOpenAI(
            model=chat_model,
            temperature=1,
            openai_api_key=api_key
        )
    
    elif active_provider == 'gemini':
        api_key = os.environ.get("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("Gemini APIキーが設定されていません")
        model_name = os.getenv('GEMINI_MODEL', 'gemini-1.5-pro-latest')

        client = genai.Client(api_key=api_key)
        return client, model_name  # 注意这里返回的是 client 和 模型名

    
    elif active_provider == 'deepseek':
        api_key = os.environ.get("DEEPSEEK_API_KEY")
        if not api_key:
            raise ValueError("Deepseek APIキーが設定されていません")
        deepseek_model = os.getenv('DEEPSEEK_MODEL', 'deepseek-chat')
        return ChatOpenAI(
            model=deepseek_model,
            api_key=api_key,
            openai_api_base="https://api.deepseek.com",
        )
    
    else:  # ollama
        from langchain.chat_models import ChatOllama
        base_url = os.getenv('OLLAMA_BASE_URL', 'http://localhost:11434')
        model = os.getenv('OLLAMA_CHAT_MODEL', 'llama2')
        return ChatOllama(
            base_url=base_url,
            model=model
        )


# CSSのスタイルを定義
st.markdown("""
    <style>
        #MainMenu {
            display: none !important;
        }
        .e17vllj40, .e10jh26i2 {
            display: none !important;
        }
        .stChatMessage img {
            width: 50px;
            height: 50px;
        }
        .st-emotion-cache-janbn0 {
            flex-direction: row-reverse;
            width: 310px;
            margin-left: auto;
        }
    </style>
    """, unsafe_allow_html=True)

# 環境変数の読み込み

def get_file_metadata(file_path):
    """ファイルのメタデータ情報を取得する"""
    try:
        stat = os.stat(file_path)
        return {
            'path': file_path,
            'mtime': stat.st_mtime,
            'size': stat.st_size,
            'hash': hashlib.md5(f"{file_path}_{stat.st_mtime}_{stat.st_size}".encode()).hexdigest()
        }
    except Exception as e:
        st.warning(f"ファイルのメタデータを取得できません ({file_path}): {e}")
        return None

def load_file_metadata_cache():
    """ファイルメタデータキャッシュの読み込み"""
    cache_file = os.path.join("vector_store", "file_metadata.json")
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            st.warning(f"ファイルメタデータキャッシュの読み込みに失敗しました: {e}")
    return {}

def save_file_metadata_cache(metadata_cache):
    """ファイルのメタデータキャッシュを保存する"""
    cache_file = os.path.join("vector_store", "file_metadata.json")
    os.makedirs(os.path.dirname(cache_file), exist_ok=True)
    try:
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(metadata_cache, f, ensure_ascii=False, indent=2)
    except Exception as e:
        st.warning(f"ファイルメタデータキャッシュの保存に失敗しました: {e}")

def save_bm25_cache(bm25_retriever, documents):
    """BM25インデックスとドキュメントをキャッシュに保存する"""
    cache_dir = "vector_store"
    os.makedirs(cache_dir, exist_ok=True)

    try:
        # BM25インデックスを保存
        bm25_cache_file = os.path.join(cache_dir, "bm25_retriever.pkl")
        with open(bm25_cache_file, 'wb') as f:
            pickle.dump(bm25_retriever, f)

        # ドキュメントを保存
        docs_cache_file = os.path.join(cache_dir, "documents.pkl")
        with open(docs_cache_file, 'wb') as f:
            pickle.dump(documents, f)

    except Exception as e:
        st.warning(f"BM25キャッシュの保存に失敗しました: {e}")

def load_bm25_cache():
    """BM25インデックスとドキュメントをキャッシュから読み込む"""
    cache_dir = "vector_store"
    bm25_cache_file = os.path.join(cache_dir, "bm25_retriever.pkl")
    docs_cache_file = os.path.join(cache_dir, "documents.pkl")

    try:
        if os.path.exists(bm25_cache_file) and os.path.exists(docs_cache_file):
            # BM25インデックスを読み込み
            with open(bm25_cache_file, 'rb') as f:
                bm25_retriever = pickle.load(f)

            # ドキュメントを読み込み
            with open(docs_cache_file, 'rb') as f:
                documents = pickle.load(f)

            return bm25_retriever, documents
    except Exception as e:
        st.warning(f"BM25キャッシュの読み込みに失敗しました: {e}")

    return None, None

def clear_bm25_cache():
    """BM25キャッシュファイルを削除する"""
    cache_dir = "vector_store"
    bm25_cache_file = os.path.join(cache_dir, "bm25_retriever.pkl")
    docs_cache_file = os.path.join(cache_dir, "documents.pkl")

    try:
        if os.path.exists(bm25_cache_file):
            os.remove(bm25_cache_file)
        if os.path.exists(docs_cache_file):
            os.remove(docs_cache_file)
    except Exception as e:
        st.warning(f"BM25キャッシュの削除に失敗しました: {e}")

def detect_file_changes(files):
    """ファイルの変更を検出し、新規、変更、削除されたファイルを返します"""
    current_metadata = {}
    for file_path in files:
        metadata = get_file_metadata(file_path)
        if metadata:
            current_metadata[file_path] = metadata

    # キャッシュメタデータの読み込み
    cached_metadata = load_file_metadata_cache()

    # 変更の検出
    new_files = []
    modified_files = []
    deleted_files = []

    # 新規ファイルと変更されたファイルの検出
    for file_path, current_meta in current_metadata.items():
        if file_path not in cached_metadata:
            new_files.append(file_path)
        elif cached_metadata[file_path]['hash'] != current_meta['hash']:
            modified_files.append(file_path)

    # 削除されたファイルの検出
    for file_path in cached_metadata:
        if file_path not in current_metadata:
            deleted_files.append(file_path)

    return {
        'new_files': new_files,
        'modified_files': modified_files,
        'deleted_files': deleted_files,
        'current_metadata': current_metadata,
        'cached_metadata': cached_metadata
    }

def load_single_file_documents(file_path):
    """単一ファイルのドキュメントの読み込み（セキュリティ検証付き）"""
    documents = []
    try:
        # ファイル名の安全性チェック
        filename = os.path.basename(file_path)
        if not is_safe_filename(filename):
            st.warning(f"🚫 安全でないファイル名が検出されました: '{filename}' - このファイルはスキップされます")
            return documents

        # ファイルの存在確認
        if not os.path.exists(file_path):
            st.warning(f"ファイルが見つかりません: '{file_path}'")
            return documents

        # ファイルサイズチェック（50MB制限）
        file_size = os.path.getsize(file_path)
        max_size = 50 * 1024 * 1024  # 50MB
        if file_size > max_size:
            st.warning(f"ファイルサイズが大きすぎます: '{filename}' ({file_size:,} bytes > {max_size:,} bytes)")
            return documents

        # 拡張子による処理分岐
        if file_path.endswith('.pdf'):
            from langchain.document_loaders import PyMuPDFLoader
            loader = PyMuPDFLoader(file_path)
            docs = loader.load()
            documents.extend(docs)
        elif file_path.endswith('.txt'):
            loader = CustomTextLoader(file_path)
            docs = loader.load()
            documents.extend(docs)
        elif file_path.endswith(('.xlsx', '.xls')):
            df = pd.read_excel(file_path, sheet_name=None)
            for sheet_name, sheet_data in df.items():
                text_content = f"シート名: {sheet_name}\n\n"
                columns = sheet_data.columns.tolist()
                batch_size = 100
                for start_idx in range(0, len(sheet_data), batch_size):
                    end_idx = min(start_idx + batch_size, len(sheet_data))
                    batch_text = ""
                    for idx in range(start_idx, end_idx):
                        row = sheet_data.iloc[idx]
                        batch_text += "データ項目:\n"
                        for col in columns:
                            if pd.notna(row[col]):
                                if isinstance(row[col], (int, float)):
                                    value = f"{row[col]:,}" if isinstance(row[col], int) else f"{row[col]:.2f}"
                                else:
                                    value = str(row[col])
                                batch_text += f"- {col}: {value}\n"
                        batch_text += "\n"
                    doc = Document(
                        page_content=batch_text,
                        metadata={
                            "source": file_path,
                            "sheet": sheet_name,
                            "type": "excel",
                            "columns": ", ".join(columns),
                            "security_checked": True
                        }
                    )
                    documents.append(doc)
        else:
            st.warning(f"サポートされていないファイル形式です: '{filename}'")
            return documents

        # 読み込み成功時のメタデータ追加
        for doc in documents:
            if doc.metadata:
                doc.metadata["security_checked"] = True
                doc.metadata["file_size"] = file_size

    except Exception as e:
        st.warning(f"ファイル '{file_path}' の読み込み中にエラーが発生しました: {e}")

    return documents

def load_all_documents(files):
    """すべてのファイルのドキュメントを読み込む"""
    documents = []
    for file_path in files:
        file_docs = load_single_file_documents(file_path)
        documents.extend(file_docs)
    return documents

def remove_documents_from_vector_store(vector_store, file_paths):
    """指定されたファイルのドキュメントをベクターストレージから削除します（削除対象としてマークし、再構築時に実際に処理します）"""
    # FAISSはメタデータによる削除を直接サポートしておらず、再構築によって削除を処理します。
    # この関数は主にマーキングに使用され、実際の削除はインデックスの再構築時に実行されます。
    return True

def load_vector_index():
    """ベクトルインデックスとBM25インデックスを取得する関数（增量更新版本）"""
    index_dir = os.path.join("vector_store")

    try:
        # ファイルの変更を検知（セキュリティフィルタリング付き）
        all_files = glob.glob(os.path.join("files", "*"))

        # 安全なファイルのみをフィルタリング
        files = []
        unsafe_files = []
        for file_path in all_files:
            filename = os.path.basename(file_path)
            if is_safe_filename(filename):
                files.append(file_path)
            else:
                unsafe_files.append(filename)

        # 危険なファイルが検出された場合の警告
        if unsafe_files:
            st.error(f"🚫 危険なファイルが検出されました（処理をスキップします）:")
            for unsafe_file in unsafe_files:
                st.error(f"• {unsafe_file}")

        # ファイルが存在しない場合はNoneを返す
        if not files:
            if unsafe_files:
                st.warning(f"安全なファイルがありません。危険なファイル {len(unsafe_files)}個がスキップされました。")
            else:
                st.warning(f"学習用ファイルがありません。{os.path.join('files')} ディレクトリにPDF、TXT、またはExcelファイルを追加してください。")
            return None

        # ファイルの変更を検知
        changes = detect_file_changes(files)
        new_files = changes['new_files']
        modified_files = changes['modified_files']
        deleted_files = changes['deleted_files']
        current_metadata = changes['current_metadata']

        # プログレスバー用のプレースホルダーを作成
        progress_placeholder = st.empty()

        # 何も変更されていない場合は、既存のインデックスをロードするだけです
        if not new_files and not modified_files and not deleted_files and os.path.exists(index_dir):
            with st.spinner("既存のインデックスを読み込んでいます..."):
                embeddings = get_embeddings()
                vector_store = FAISS.load_local(
                    index_dir,
                    embeddings,
                    allow_dangerous_deserialization=True
                )

                # BM25キャッシュから読み込みを試行
                bm25_retriever, documents = load_bm25_cache()

                if bm25_retriever is None or documents is None:
                    # キャッシュが無い場合は従来通り再作成
                    st.info("BM25キャッシュが見つかりません。再作成中...")
                    documents = load_all_documents(files)
                    bm25_retriever = BM25Retriever.from_documents(documents)
                    # 次回のためにキャッシュを保存
                    save_bm25_cache(bm25_retriever, documents)
                else:
                    # st.info("")
                    pass

                progress_placeholder.empty()
                # st.success("✅ 既存のインデックスを読み込みました（変更なし）")
                return {"vector_store": vector_store, "bm25_retriever": bm25_retriever, "documents": documents}

        # 増分更新の処理
        embeddings = get_embeddings()
        vector_store = None

        # 削除されたファイルがある場合、または初めて作成されたファイルがある場合は、インデックスを再構築する必要があります。
        need_rebuild = deleted_files or not os.path.exists(index_dir)

        # ファイルに変更がある場合はBM25キャッシュをクリア
        if new_files or modified_files or deleted_files:
            clear_bm25_cache()

        if not need_rebuild:
            # 既存のベクトルストレージをロードしてみる
            try:
                vector_store = FAISS.load_local(
                    index_dir,
                    embeddings,
                    allow_dangerous_deserialization=True
                )
                st.info("✅ 既存のベクトルストアを読み込みました")
            except Exception as e:
                st.warning(f"既存のベクトルストアの読み込みに失敗しました: {e}")
                need_rebuild = True

        # テキストスプリッターの設定
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=500,
            chunk_overlap=50,
            length_function=len,
            separators=["\n\n", "\n", "。", "、", " ", ""]
        )

        # 新規ファイルと変更されたファイルの処理
        files_to_process = new_files + modified_files

        if files_to_process:
            st.info(f"📝 処理対象: 新規ファイル {len(new_files)}個、更新ファイル {len(modified_files)}個")

            # 更新が必要なファイルの処理
            new_documents = []
            progress_bar = progress_placeholder.progress(0)

            for i, file_path in enumerate(files_to_process):
                try:
                    st.info(f"処理中: {os.path.basename(file_path)}")

                    # 単一ファイルのドキュメントの読み込み
                    file_docs = load_single_file_documents(file_path)

                    # ドキュメントを分割
                    for doc in file_docs:
                        try:
                            if file_path.endswith('.txt'):
                                # txtファイルにはエラー処理を使用する
                                valid_chunks = split_documents_with_error_handling(text_splitter, [doc], file_path)
                                new_documents.extend(valid_chunks)
                            else:
                                # 他のファイルタイプには標準のセグメンテーションを使用する
                                split_docs = text_splitter.split_documents([doc])
                                new_documents.extend(split_docs)
                        except Exception as e:
                            st.warning(f"ドキュメントの分割中にエラーが発生しました（{file_path}）: {e}")
                            # より小さなチャンクサイズを試し
                            smaller_splitter = RecursiveCharacterTextSplitter(
                                chunk_size=250,
                                chunk_overlap=25,
                                length_function=len,
                                separators=["\n\n", "\n", "。", "、", " ", ""]
                            )
                            try:
                                if file_path.endswith('.txt'):
                                    valid_chunks = split_documents_with_error_handling(smaller_splitter, [doc], file_path)
                                    new_documents.extend(valid_chunks)
                                else:
                                    split_docs = smaller_splitter.split_documents([doc])
                                    new_documents.extend(split_docs)
                            except Exception as e2:
                                st.error(f"ドキュメントの分割に失敗しました（{file_path}）: {e2}")

                    progress = (i + 1) / len(files_to_process)
                    progress_bar.progress(progress)

                except Exception as e:
                    st.warning(f"ファイル '{file_path}' の処理中にエラーが発生しました: {e}")

            # 新しいドキュメントをベクターストレージに追加
            if new_documents:
                st.info(f"📊 新しいドキュメント {len(new_documents)}個をインデックスに追加中...")

                if vector_store is None or need_rebuild:
                    # 新しいベクトルストレージを作成する
                    vector_store = process_documents_with_rate_limiting(
                        new_documents,
                        embeddings,
                        progress_placeholder
                    )
                else:
                    # 既存のベクトルストレージに追加する
                    try:
                        vector_store.add_documents(new_documents)
                        st.success(f"✅ {len(new_documents)}個のドキュメントを既存のインデックスに追加しました")
                    except Exception as e:
                        st.warning(f"インデックスへの追加に失敗しました: {e}")
                        st.info("インデックスを再構築します...")
                        # 増分追加が失敗した場合は、インデックス全体を再構築します
                        all_documents = load_all_documents(files)
                        processed_docs = []
                        for doc in all_documents:
                            try:
                                if doc.metadata.get('source', '').endswith('.txt'):
                                    valid_chunks = split_documents_with_error_handling(text_splitter, [doc], doc.metadata.get('source', ''))
                                    processed_docs.extend(valid_chunks)
                                else:
                                    split_docs = text_splitter.split_documents([doc])
                                    processed_docs.extend(split_docs)
                            except Exception as e:
                                st.warning(f"ドキュメント処理エラー: {e}")

                        vector_store = process_documents_with_rate_limiting(
                            processed_docs,
                            embeddings,
                            progress_placeholder
                        )

        elif need_rebuild:
            # 再構築が必要だが、新しいファイルがない（削除のみ）状況
            st.info("🔄 インデックスを再構築しています...")
            all_documents = load_all_documents(files)
            processed_docs = []
            for doc in all_documents:
                try:
                    if doc.metadata.get('source', '').endswith('.txt'):
                        valid_chunks = split_documents_with_error_handling(text_splitter, [doc], doc.metadata.get('source', ''))
                        processed_docs.extend(valid_chunks)
                    else:
                        split_docs = text_splitter.split_documents([doc])
                        processed_docs.extend(split_docs)
                except Exception as e:
                    st.warning(f"ドキュメント処理エラー: {e}")

            vector_store = process_documents_with_rate_limiting(
                processed_docs,
                embeddings,
                progress_placeholder
            )

        # ベクターストレージとメタデータキャッシュを保存する
        if vector_store is not None:
            try:
                os.makedirs(index_dir, exist_ok=True)
                vector_store.save_local(index_dir)

                # ファイルのメタデータキャッシュを保存する
                save_file_metadata_cache(current_metadata)

                st.success("✅ ベクトルインデックスを保存しました")
            except Exception as e:
                st.warning(f"ベクトルインデックスの保存に失敗しました: {e}")

        # BM25 インデックスを作成します
        try:
            all_documents = load_all_documents(files)
            bm25_retriever = BM25Retriever.from_documents(all_documents)
            # BM25キャッシュを保存
            save_bm25_cache(bm25_retriever, all_documents)
            st.success("✅ BM25インデックスを作成しました")
        except Exception as e:
            st.warning(f"BM25インデックスの作成に失敗しました: {e}")
            bm25_retriever = None
            all_documents = []

        progress_placeholder.empty()

        # 処理結果の概要を表示します
        if new_files or modified_files or deleted_files:
            st.info(f"📊 処理完了: 新規 {len(new_files)}個、更新 {len(modified_files)}個、削除 {len(deleted_files)}個")

        return {"vector_store": vector_store, "bm25_retriever": bm25_retriever, "documents": all_documents}
        
    except Exception as e:
        st.error(f"ベクトルインデックスの作成中にエラーが発生しました: {e}")
        if 'progress_placeholder' in locals():
            progress_placeholder.empty()
        return None

def truncate_text(text, max_chars=50000):
    """テキストを指定された文字数で切り詰める"""
    if len(text) <= max_chars:
        return text

    # 文の境界で切り詰めを試行
    truncated = text[:max_chars]
    last_sentence_end = max(
        truncated.rfind('。'),
        truncated.rfind('！'),
        truncated.rfind('？'),
        truncated.rfind('\n')
    )

    if last_sentence_end > max_chars * 0.8:  # 80%以上の位置で文の境界が見つかった場合
        return truncated[:last_sentence_end + 1] + "\n[...内容が長いため省略されました...]"
    else:
        return truncated + "\n[...内容が長いため省略されました...]"

def estimate_tokens(text):
    """テキストのトークン数を概算する（日本語対応）"""
    # 日本語の場合、1文字あたり約1.5-2トークンと仮定
    # 英語の場合、1単語あたり約1.3トークンと仮定
    japanese_chars = len([c for c in text if ord(c) > 127])
    english_chars = len(text) - japanese_chars

    # 概算計算
    estimated_tokens = japanese_chars * 1.8 + english_chars * 0.25
    return int(estimated_tokens)

def generate_answer(prompt, retriever_bundle):
    user_question = prompt
    chat_model_result = get_chat_model()

    # ユーザーの質問をすぐに表示
    st.session_state.history.append({"user": user_question, "ai": "", "check": False})

    # 回答生成中の表示
    with st.spinner("回答を生成中..."):
        try:
            conversation_history = ""
            last_ai_answer = ""
            if len(st.session_state.history) > 1:
                last_chat = st.session_state.history[-2]
                if "ai" in last_chat:
                    last_ai_response = last_chat["ai"]+"\n\n"
                    last_ai_answer += last_ai_response
            
            # 検索クエリの作成を改善
            keywords = set(prompt.replace("について", " ")
                               .replace("は", " ")
                               .replace("の", " ")
                               .replace("を", " ")
                               .replace("に", " ")
                               .replace("が", " ")
                               .split())
            important_words = [w for w in keywords if len(w) > 1]
            search_query = f"{prompt} {' '.join(keywords)} {' '.join(important_words)}"

            # --- Hybrid Retrieval ---
            vector_store = retriever_bundle["vector_store"]
            bm25_retriever = retriever_bundle["bm25_retriever"]

            # FAISS dense retrieval
            faiss_docs = vector_store.similarity_search(
                search_query,
                k=10,
                fetch_k=20
            )
            # BM25 sparse retrieval
            bm25_docs = bm25_retriever.get_relevant_documents(
                search_query
            )
            # Merge and deduplicate (by content hash)
            all_docs = faiss_docs + bm25_docs
            unique_docs = []
            seen_content = set()
            for doc in all_docs:
                content_hash = hash(doc.page_content[:50])
                if content_hash not in seen_content:
                    seen_content.add(content_hash)
                    unique_docs.append(doc)
            # Select top 5 for context
            selected_docs = unique_docs[:5]

            # コンテキストを作成（長さ制限付き）
            context_parts = []
            total_context_chars = 0
            max_context_chars = 30000  # コンテキストの最大文字数

            for i, doc in enumerate(selected_docs):
                doc_content = doc.page_content
                # 各ドキュメントも個別に制限
                if len(doc_content) > 8000:
                    doc_content = truncate_text(doc_content, 8000)

                doc_text = f"参考文書{i+1}:\n{doc_content}"

                # 総文字数チェック
                if total_context_chars + len(doc_text) > max_context_chars:
                    remaining_chars = max_context_chars - total_context_chars
                    if remaining_chars > 1000:  # 最低1000文字は確保
                        doc_text = f"参考文書{i+1}:\n{truncate_text(doc_content, remaining_chars - 100)}"
                        context_parts.append(doc_text)
                    break

                context_parts.append(doc_text)
                total_context_chars += len(doc_text)

            context = "\n\n".join(context_parts)

            # Extract unique file names from the selected_docs metadata
            unique_files = list({doc.metadata['source'] for doc in selected_docs})

            # Generate the context title with the unique file names
            context_title = "\n\n".join([
                f"参考文書{i+1}: {file_name.split('/')[-1].split('\\')[-1]}"  # Extract the file name only
                for i, file_name in enumerate(unique_files)
            ])

            # システムプロンプトを調整
            system_prompt = """あなたは丁寧で正確な回答を提供する、自然な会話ができるアシスタントです。
以下の指示に従って回答してください：

1. 会話の文脈を理解し、自然な流れで会話を続けてください
2. 前回の会話内容を踏まえて、関連性のある情報を提供してください
3. ユーザーの質問に対して、具体的で分かりやすい回答を心がけてください
4. 必要に応じて、前回の会話内容に関連する追加情報や質問を提案してください
5. 提供された文脈を参考に、日本語で質問に対して具体的に回答してください
6. 提供された文脈に無い情報はその旨を回答するようにしてください
7. 参考にした文脈の引用は回答しないでください
8. 必要に応じて、参考にした文書の内容を引用してください
9. 質問の意図を理解し、関連する情報があれば柔軟に解釈して回答してください
10. 文脈内の情報が部分的にでも質問に関連する場合は、その情報を活用して回答してください"""

            # 会話履歴の管理を改善（長さ制限付き）
            conversation_history = ""
            if len(st.session_state.history) > 1:
                # 直近の2つの会話を取得して文脈として使用（3つから2つに削減）
                recent_chats = st.session_state.history[-2:]
                conversation_history = "前回までの会話：\n"
                total_history_chars = 0
                max_history_chars = 5000  # 会話履歴の最大文字数

                for chat in recent_chats:
                    if "user" in chat and "ai" in chat:
                        user_text = chat['user']
                        ai_text = chat['ai']

                        # AI回答が長すぎる場合は切り詰め
                        if len(ai_text) > 2000:
                            ai_text = truncate_text(ai_text, 2000)

                        chat_text = f"ユーザー: {user_text}\nアシスタント: {ai_text}\n\n"

                        if total_history_chars + len(chat_text) > max_history_chars:
                            break

                        conversation_history += chat_text
                        total_history_chars += len(chat_text)

            # 最終的なプロンプトを構築
            full_context = f"""現在の文脈：
{context}

現在の質問：
{user_question}"""

            # 会話履歴と現在のコンテキストを組み合わせ
            if conversation_history.strip():
                conversation_history += f"\n{full_context}"
            else:
                conversation_history = full_context

            # 最終的な長さチェック
            max_total_chars = 40000  # 全体の最大文字数
            if len(conversation_history) > max_total_chars:
                conversation_history = truncate_text(conversation_history, max_total_chars)

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": conversation_history}
            ]
            
            if isinstance(chat_model_result, tuple):
                # Gemini 用法
                client, model_name = chat_model_result
                prompt_text = "\n".join([f"{m['role'].capitalize()}: {m['content']}" for m in messages])

                # トークン制限チェック
                estimated_tokens = estimate_tokens(prompt_text)
                max_tokens = 200000  # 安全マージンを設けて100万トークン

                if estimated_tokens > max_tokens:
                    st.warning(f"⚠️ プロンプトが長すぎます（推定{estimated_tokens:,}トークン）。コンテキストを削減します。")
                    # さらに短縮
                    conversation_history = truncate_text(conversation_history, 20000)
                    messages = [
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": conversation_history}
                    ]
                    prompt_text = "\n".join([f"{m['role'].capitalize()}: {m['content']}" for m in messages])
                    estimated_tokens = estimate_tokens(prompt_text)
                    st.info(f"📊 削減後のトークン数: 推定{estimated_tokens:,}トークン")

                response = client.models.generate_content(
                    model=model_name,
                    contents=prompt_text
                )
                answer = response.text
            else:
                # LangChain 兼容模型（OpenAI、Deepseek、Ollama）
                chat_model = chat_model_result
                response = chat_model.invoke(messages)
                answer = response.content if hasattr(response, 'content') else str(response)

            if answer.strip() == "":
                answer = "申し訳ありませんが、その質問にはお答えできません。"
                context_title = ""
            
            # 最後の履歴を更新
            st.session_state.history[-1]["ai"] = answer
            
        except Exception as e:
            st.error(f"エラーが発生しました: {e}")
            # エラー時も履歴を更新
            st.session_state.history[-1]["ai"] = "申し訳ありません。回答の生成中にエラーが発生しました。"
            answer = f"申し訳ありません。回答の生成中にエラーが発生しました。{e}"
            context_title = ""
        finally:
            aichat(user_question, answer, context_title)

def display_message():
    for index, chat in enumerate(st.session_state.history):
        if "user" in chat and "ai" in chat and "check" in chat:
            # ユーザーのメッセージを表示
            with st.chat_message("user", avatar="img/user.png"):
                st.write(chat["user"])
            
            # AIの回答を表示
            if chat["check"] == "blank":
                message("画像生成しました→[こちらをクリック]("+chat["ai"]+")", is_user=False, key=f"ai_{index}")
            else:
                ai_chat = chat["ai"]
                if ai_chat:  # 空でない場合のみ表示
                    if index == len(st.session_state.history) - 1:
                        with st.chat_message("ai", avatar="img/ai.png"):
                            assistant_msg = ""
                            assistant_response_area = st.empty()
                            for chunk in ai_chat:
                                assistant_msg += chunk
                                assistant_response_area.write(assistant_msg)
                                time.sleep(0.005)
                    else:
                        with st.chat_message("ai", avatar="img/ai.png"):
                            st.write(ai_chat)

def init_db():
    conn = sqlite3.connect('aichat_log.db')
    c = conn.cursor()
    c.execute('''
        CREATE TABLE IF NOT EXISTS logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_question TEXT NOT NULL,
            ai_answer TEXT NOT NULL,
            reference_document TEXT,
            timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    conn.commit()
    conn.close()

def aichat(user_question, ai_answer, reference_document=None):
    # Process the user's question and generate an AI response

    # Log the interaction
    conn = sqlite3.connect('aichat_log.db')
    c = conn.cursor()
    c.execute('''
        INSERT INTO logs (user_question, ai_answer, reference_document)
        VALUES (?, ?, ?)
    ''', (user_question, ai_answer, reference_document))
    conn.commit()
    conn.close()

    return ai_answer

def main_chat_interface():
    # セッション状態の初期化
    if "history" not in st.session_state:
        st.session_state.history = []

    try:
        # ベクトルインデックスの読み込み
        retriever_bundle = load_vector_index()
        
        if retriever_bundle is None:
            st.error("ベクトルインデックスの初期化に失敗しました")
            return
        
        if prompt := st.chat_input("質問を入力してください"):
            generate_answer(prompt, retriever_bundle)
    
    except Exception as e:
        st.error(f"エラーが発生しました: {e}")
        return


def app_logic():
    if not st.session_state.get("authenticated", False):
        # 持久化された認証失敗記録をチェック
        is_locked, remaining_time = check_auth_lockout()

        if is_locked:
            st.error("🔒 セキュリティロックアウト")
            st.warning(f"パスワードを3回間違えました。あと {int(remaining_time)} 秒待ってから再試行してください。")
            st.info("💡 ヒント: ページを刷新してもロックアウト時間は変わりません。")

            # 残り時間を表示するプログレスバー
            progress = 1 - (remaining_time / 300)  # 300秒が最大
            st.progress(progress, text=f"ロックアウト解除まで: {int(remaining_time)}秒")

            # 自動更新のため
            time.sleep(1)
            st.rerun()
        else:
            with st.form(key="login_form"):
                password_input = st.text_input("パスワード", type="password", key="password_for_login") # Translated
                login_button = st.form_submit_button("ログイン") # Translated

                if login_button:
                    if password_input:
                        entered_password_hash = hashlib.sha256(password_input.encode()).hexdigest()
                        if entered_password_hash == STORED_PASSWORD_HASH:
                            st.session_state.authenticated = True
                            reset_auth_failures()  # 成功したのでリセット
                            st.rerun()
                        else:
                            # 持久化された認証失敗を記録
                            attempts = record_auth_failure()
                            st.error("❌ パスワードが正しくありません。")

                            if attempts >= 3:
                                st.error("🔒 セキュリティロックアウト: 3回連続でパスワードを間違えました。")
                                st.warning("⏰ 5分間（300秒）待ってから再試行してください。")
                                st.info("💡 ページを刷新してもロックアウト時間は変わりません。")
                            else:
                                remaining_attempts = 3 - attempts
                                st.warning(f"⚠️ 残り試行回数: {remaining_attempts}回")
                    else:
                        st.warning("パスワードを入力してください。") # Translated
        return # Stop further execution if login form is displayed or on failed attempt before rerun

    # If authenticated:
    main_chat_interface()
    display_message()
    init_db() # Initialize DB after successful login

if __name__ == "__main__":
    # Initialize session state variables
    if "history" not in st.session_state:
        st.session_state.history = []
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False

    app_logic()

