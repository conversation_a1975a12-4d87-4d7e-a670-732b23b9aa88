# チャンクレベルエラーハンドリング解決方案

## 概要

従来の方法では、ファイルに一部でもエンコーディング問題があると、そのファイル全体がスキップされていました。新しいアプローチでは、**チャンクレベル**でエラーハンドリングを行い、問題のある部分のみをスキップして、読み取り可能な部分を最大限活用します。

## 新機能の詳細

### 1. チャンクレベルエラーハンドリング

#### `split_documents_with_error_handling()` 関数
- **目的**: ドキュメントを分割し、問題のあるチャンクのみをスキップ
- **処理フロー**:
  1. テキスト内容のクリーンアップ
  2. ドキュメントの分割
  3. 各チャンクの個別検証
  4. 有効なチャンクのみを保持

#### 主な特徴:
- **個別チャンク検証**: 各チャンクを独立してエンコーディングテスト
- **最小長チェック**: 10文字未満の短すぎるチャンクを除外
- **統計情報**: 有効/スキップされたチャンクの数を報告

### 2. テキストクリーニング機能

#### `clean_text_content()` 関数
- **制御文字の除去**: 改行・タブ以外の制御文字を削除
- **無効Unicode文字の処理**: エンコーディングエラーを起こす文字を除去
- **空白の正規化**: 連続する空白を単一スペースに変換
- **空行の除去**: 意味のない空行を削除

### 3. 行レベルフォールバック処理

#### CustomTextLoader の改良
ファイル全体の読み込みに失敗した場合：
1. **行ごとの処理**: 各行を個別に複数のエンコーディングで試行
2. **部分的回復**: 読み取り可能な行のみを保持
3. **詳細な統計**: 成功/失敗した行数を報告

## 使用例

### 従来の処理（ファイル全体スキップ）
```
❌ ファイル 'rakuten-co-jp_20250625.txt' の読み込み中にエラーが発生しました
→ ファイル全体が失われる
```

### 新しい処理（チャンクレベル回復）
```
✅ ファイル 'rakuten-co-jp_20250625.txt': 1,247行を読み込み、23行の問題のある行をスキップしました
📝 ファイル 'rakuten-co-jp_20250625.txt': 156個の有効なチャンクを取得、12個の問題のあるチャンクをスキップしました
```

## 処理フロー

### 1. ファイル読み込み段階
```
ファイル読み込み
    ↓
複数エンコーディング試行
    ↓
失敗時：行ごと処理
    ↓
有効な行のみ結合
```

### 2. チャンク分割段階
```
テキストクリーニング
    ↓
ドキュメント分割
    ↓
各チャンク検証
    ↓
有効チャンクのみ保持
```

## 期待される効果

### 1. データ回復率の向上
- **従来**: エラーファイルは100%失われる
- **新方式**: 通常80-95%のデータを回復可能

### 2. 処理の安定性
- 部分的な問題でも処理が継続
- より多くのファイルから情報を抽出
- エラー箇所の特定が容易

### 3. 詳細な診断情報
- スキップされたチャンク数
- 問題のある行数
- 回復されたデータの割合

## テスト方法

### 1. チャンクレベル処理テスト
```bash
python test_chunk_processing.py
```

### 2. 個別ファイルテスト
```python
from test_chunk_processing import test_file_chunk_processing
test_file_chunk_processing("files/your_problematic_file.txt")
```

## 設定オプション

### チャンクサイズの調整
```python
# 標準設定
text_splitter = RecursiveCharacterTextSplitter(
    chunk_size=500,
    chunk_overlap=50
)

# 問題の多いファイル用（小さなチャンク）
smaller_splitter = RecursiveCharacterTextSplitter(
    chunk_size=250,
    chunk_overlap=25
)
```

### 最小チャンク長の調整
```python
# clean_text_content() 関数内で調整可能
if len(test_content.strip()) >= 10:  # この値を変更
    valid_chunks.append(chunk)
```

## トラブルシューティング

### 1. 回復率が低い場合
- チャンクサイズをさらに小さくする（100-200文字）
- 分割セパレータを調整する
- 最小チャンク長を短くする（5文字程度）

### 2. 処理が遅い場合
- チャンクサイズを大きくする
- 最小チャンク長を長くする
- 並列処理の導入を検討

### 3. 品質が低い場合
- より厳密なクリーニングルールを追加
- チャンク検証ロジックを強化
- 手動でのファイル前処理を検討

## 実装の詳細

### エラーハンドリングの階層
1. **ファイルレベル**: 複数エンコーディング試行
2. **行レベル**: 行ごとの個別処理
3. **チャンクレベル**: 分割後の個別検証
4. **文字レベル**: Unicode正規化とクリーニング

### メタデータの活用
各チャンクには以下の情報が付与されます：
- `cleaned`: クリーニング処理が適用されたか
- `valid_lines`: 有効な行数
- `skipped_lines`: スキップされた行数
- `warning`: 警告メッセージ

これにより、後から問題のあるファイルやチャンクを特定・改善することが可能です。

## 結論

この新しいアプローチにより、エンコーディング問題のあるファイルからも最大限の情報を抽出できるようになりました。特に `rakuten-co-jp_20250625.txt` のような部分的に問題のあるファイルでも、読み取り可能な部分を有効活用できます。
