"""
速率限制設定ファイル
OpenAI APIの速率限制を管理するための設定
"""

# 速率限制設定
RATE_LIMIT_CONFIG = {
    # バッチサイズ設定
    'default_batch_size': 50,  # デフォルトのバッチサイズ（小さくして速率限制を回避）
    'small_batch_size': 25,    # エラー時の小さなバッチサイズ
    'min_batch_size': 10,      # 最小バッチサイズ
    
    # 待機時間設定
    'base_wait_time': 2.0,     # 基本待機時間（秒）
    'batch_interval': 0.5,     # バッチ間の待機時間（秒）
    'error_wait_buffer': 0.5,  # エラー時の追加待機時間（秒）
    
    # リトライ設定
    'max_retries': 3,          # 最大リトライ回数
    'retry_multiplier': 1.5,   # リトライ時の待機時間倍率
    
    # プログレス表示設定
    'show_progress': True,     # プログレスバーを表示するか
    'show_batch_info': True,   # バッチ情報を表示するか
}

# モデル別の推奨設定
MODEL_SPECIFIC_CONFIG = {
    'text-embedding-3-small': {
        'batch_size': 50,
        'tokens_per_minute': 1000000,  # TPM制限
        'requests_per_minute': 3000,   # RPM制限
    },
    'text-embedding-3-large': {
        'batch_size': 30,
        'tokens_per_minute': 1000000,
        'requests_per_minute': 3000,
    },
    'text-embedding-ada-002': {
        'batch_size': 40,
        'tokens_per_minute': 1000000,
        'requests_per_minute': 3000,
    }
}

def get_model_config(model_name):
    """
    モデル名に基づいて推奨設定を取得
    """
    return MODEL_SPECIFIC_CONFIG.get(model_name, MODEL_SPECIFIC_CONFIG['text-embedding-3-small'])

def calculate_optimal_batch_size(document_count, model_name='text-embedding-3-small'):
    """
    ドキュメント数とモデルに基づいて最適なバッチサイズを計算
    """
    model_config = get_model_config(model_name)
    base_batch_size = model_config['batch_size']
    
    # ドキュメント数が少ない場合はバッチサイズを調整
    if document_count < 100:
        return min(base_batch_size, max(10, document_count // 5))
    elif document_count < 500:
        return min(base_batch_size, 30)
    else:
        return base_batch_size

def estimate_processing_time(document_count, model_name='text-embedding-3-small'):
    """
    処理時間の概算を計算
    """
    config = get_model_config(model_name)
    batch_size = calculate_optimal_batch_size(document_count, model_name)
    
    total_batches = (document_count + batch_size - 1) // batch_size
    
    # 基本処理時間 + バッチ間待機時間 + 予備時間
    base_time = total_batches * 2  # 1バッチあたり約2秒
    wait_time = total_batches * RATE_LIMIT_CONFIG['batch_interval']
    buffer_time = total_batches * 0.5  # 予備時間
    
    total_time = base_time + wait_time + buffer_time
    
    return {
        'estimated_minutes': total_time / 60,
        'total_batches': total_batches,
        'batch_size': batch_size
    }
